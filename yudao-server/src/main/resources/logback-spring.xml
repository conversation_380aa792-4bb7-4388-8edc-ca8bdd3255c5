<?xml version="1.0" encoding="UTF-8"?>
<!-- 这个是根配置文件，一定要有的
    scan：
        是当配置文件被修改后会被重新加载
    scanPeriod:
        设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，
        默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。
    debug：
        当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。
        默认值为false。
     -->
<configuration>
    <!-- 日志存放路径
        下面的标签可以自己定义
        name：相当于Map的key
        value：就是map的value
        ${catalina.base}是tomcat的当前路径
        /logs：就是tomcat下的日志路径，
        /ehrlog：如果没有目录会默认创建
    -->
    <property name="LOG_HOME" value="./logs/"/>
    <!-- appender：
        name相当于一个名称
        class：确定要加载哪个类
        encoder：一定要加 encoder，
        默认配置为PatternLayoutEncoder
        patter：必填
        ConsoleAppender：输出在控制台上
    -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度，%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %highlight(%-5level) %cyan(%logger{50}) - %highlight(%msg%n)
            </pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 当前日志文件 -->
        <file>${LOG_HOME}etadm-dp.log</file>
        <!-- 编码 -->
        <!--<Encoding>UTF-8</Encoding>-->
        <!-- 按照时间来 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${LOG_HOME}etadm-dp.%d{yyyy-MM-dd}.%i.log.gz</FileNamePattern>
            <!--日志文件保留天数-->
            <MaxHistory>180</MaxHistory>
            <maxFileSize>10MB</maxFileSize>
            <totalSizeCap>1024MB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <!-- 布局 -->
        <encoder>
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度，%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.S`SS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
        <append>false</append>
    </appender>
    <root level="info">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>