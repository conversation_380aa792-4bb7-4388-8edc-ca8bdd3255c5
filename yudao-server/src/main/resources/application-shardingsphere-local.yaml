databaseName: shardingsphereDB
dataSources:
  db_162:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    jdbcUrl: ************************************************************************************************************************************************************************************************************************************************************************************************************
    driverClassName: com.mysql.cj.jdbc.Driver
    username: root
    password: mysql_G3nWZC
  db_1:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    jdbcUrl: ***********************************************************************************************************************************************************************************************************************************************************************************************************
    driverClassName: com.mysql.cj.jdbc.Driver
    username: root
    password: mysql_G3nWZC
  # 具体规则配置
rules:
  - !SHARDING
    tables: # 数据分片规则配置
      jy_contract_monthly: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_contract_monthly # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_contract_monthly_detail: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_contract_monthly_detail # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_contract_sequence_monthly: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_contract_sequence_monthly # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_periodic_settlement: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_periodic_settlement # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_periodic_settlement_detail: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_periodic_settlement_detail # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_section_limit_change: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_section_limit_change # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_startup_and_down_unit: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_startup_and_down_unit # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_similar_day_prediction: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_similar_day_prediction # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_startup_and_down_unit_detail: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_startup_and_down_unit_detail # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_trading_announcement: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_trading_announcement # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_substation: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_substation # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_section: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_section # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_section_congestion_record: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_section_congestion_record # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_transaction_declaration: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_transaction_declaration # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_transaction_inter_prov: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_transaction_inter_prov # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_transaction_intra_prov: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_transaction_intra_prov # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_market_trading_info: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_market_trading_info # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_contracts: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_contracts # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_contracts_equipment: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_contracts_equipment # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_contracts_member: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_contracts_member # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_contracts_month12: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_contracts_month12 # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_contracts_time24: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_contracts_time24 # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_contracts_power_curve: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_contracts_power_curve # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称
      jy_series_contracts: # 逻辑表名称
        actualDataNodes: db_${[1,162]}.jy_series_contracts # 由数据源名 + 表名组成（参考 Inline 语法规则）
        databaseStrategy: # 分库策略，缺省表示使用默认分库策略，以下的分片策略只能选其一
          standard: # 用于单分片键的标准分片场景
            shardingColumn: tenant_id # 分片列名称
            shardingAlgorithmName: database_inline # 分片算法名称

    defaultDatabaseStrategy: # 默认数据库分片策略
      standard:
        shardingColumn: tenant_id
        shardingAlgorithmName: database_inline
    shardingAlgorithms:
      database_inline:
        type: INLINE
        props:
          algorithm-expression: db_${tenant_id}
