<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.et.dal.mysql.contracts.ContractsMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <insert id="replaceInto" keyProperty="id"
            parameterType="cn.iocoder.yudao.module.et.dal.dataobject.contracts.ContractsDO">
        REPLACE INTO jy_contracts ( name, contracts_type_id, period, gen_type, series_contracts_id, include_tax,
        quantity, price, avg_grid_price, approved_price, price_difference, paper_contracts_code, contracts_code,
        trading_code, begin_time, end_time, signing_status, record_status, signing_date, record_date, history_contract,
        tenant_id, dept_id, org_code, gather_date, modification_time, deleted)
        VALUES(
        #{name,jdbcType=VARCHAR}, #{contractsTypeId,jdbcType=REAL}, #{period,jdbcType=VARCHAR}
        , #{genType,jdbcType=VARCHAR}, #{seriesContractsId,jdbcType=REAL}, #{includeTax,jdbcType=REAL}
        ,#{quantity,jdbcType=REAL}, #{price,jdbcType=REAL},
        #{avgGridPrice,jdbcType=REAL},#{approvedPrice,jdbcType=REAL}
        , #{priceDifference,jdbcType=REAL}, #{paperContractsCode,jdbcType=VARCHAR}, #{contractsCode,jdbcType=VARCHAR}
        ,#{tradingCode,jdbcType=VARCHAR}
        , #{beginTime,jdbcType=DATE}, #{endTime,jdbcType=DATE}, #{signingStatus,jdbcType=VARCHAR}
        ,#{recordStatus,jdbcType=VARCHAR}
        , #{signingDate,jdbcType=DATE}, #{recordDate,jdbcType=DATE}, #{historyContract,jdbcType=REAL}
        , #{tenantId,jdbcType=REAL}, #{deptId,jdbcType=REAL}, #{orgCode,jdbcType=VARCHAR},
        #{gatherDate,jdbcType=DATE}
        ,now(), #{deleted,jdbcType=REAL});
    </insert>
</mapper>
