package cn.iocoder.yudao.module.et.controller.admin.startupanddownunitdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Schema(description = "管理后台 - 必开必停机组明细信息新增/修改 Request VO")
@Data
public class StartupAndDownUnitDetailSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2421")
    private Long id;

    @Schema(description = "父ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3992")
    @NotNull(message = "父ID不能为空")
    private Long pid;

    @Schema(description = "必开 1 必停 0", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "必开 1 必停 0不能为空")
    private String type;

    @Schema(description = "业务时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务时间不能为空")
    private Date bizDate;

    @Schema(description = "机组台数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "机组台数不能为空")
    private Integer units;

    @Schema(description = "电压等级(kV)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "电压等级(kV)不能为空")
    private BigDecimal voltageLevel;

    @Schema(description = "原因", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "原因不能为空")
    private String cause;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime dateBegin;

    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime dateEnd;

    @Schema(description = "部门ID", example = "13853")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "数据爬取时间")
    private LocalDateTime gatherDate;

    @Schema(description = "修改时间")
    private LocalDateTime modificationTime;

}
