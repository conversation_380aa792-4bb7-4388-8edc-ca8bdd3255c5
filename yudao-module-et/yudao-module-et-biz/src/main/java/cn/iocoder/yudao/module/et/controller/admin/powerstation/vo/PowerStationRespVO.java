package cn.iocoder.yudao.module.et.controller.admin.powerstation.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 场站信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PowerStationRespVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "31824")
    @ExcelProperty("自增主键")
    private Integer id;

    @Schema(description = "场站编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("场站编码")
    private String code;

    @Schema(description = "场站简称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("场站简称")
    private String name;

    @Schema(description = "场站全称", example = "王五")
    @ExcelProperty("场站全称")
    private String fullName;

    @Schema(description = "场站调度名称", example = "芋艿")
    @ExcelProperty("场站调度名称")
    private String dispatchName;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "场站类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("场站类型")
    private String type;

    @Schema(description = "场站装机容量")
    @ExcelProperty("场站装机容量")
    private Double capacity;

    @Schema(description = "场站设备台数")
    @ExcelProperty("场站设备台数")
    private Integer quantity;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否禁用")
    private Boolean enabled;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "部门ID", example = "24667")
    @ExcelProperty("部门ID")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    @ExcelProperty("上级机构唯一编码（dept扩展字段）")
    private String orgCode;

}