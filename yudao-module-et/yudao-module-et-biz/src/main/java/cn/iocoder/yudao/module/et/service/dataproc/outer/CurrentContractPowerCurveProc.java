package cn.iocoder.yudao.module.et.service.dataproc.outer;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.et.dal.dataobject.contracts.ContractsDO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractspowercurve.ConstructsPowerCurvePointPriceDO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractspowercurve.ContractsPowerCurveDO;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingUnitOrg.TradingUnitOrgDO;
import cn.iocoder.yudao.module.et.dal.mysql.contracts.ContractsMapper;
import cn.iocoder.yudao.module.et.dal.mysql.contractspowercurve.ContractsPowerCurveMapper;
import cn.iocoder.yudao.module.et.dal.mysql.tradingUnitOrg.TradingUnitOrgMapper;
import cn.iocoder.yudao.module.et.enums.constants.Global;
import cn.iocoder.yudao.module.et.service.dataproc.base.BaseDataProc;
import cn.iocoder.yudao.module.et.service.mongo.MongoService;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.system.dal.mysql.dept.DeptMapper;
import cn.iocoder.yudao.module.system.service.dept.DeptService;
import cn.jy.soft.remote.bean.SocketPacket;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * GS-A-57 我的合同-当前合同-电力曲线
 *
 * <AUTHOR>
 * @date 2025-03-16
 **/
@Slf4j
@Service
@AllArgsConstructor

public class CurrentContractPowerCurveProc implements BaseDataProc {
    @Resource
    public ContractsMapper contractsMapper;
    @Resource
    public ContractsPowerCurveMapper contractsPowerCurveMapper;
    @Resource
    public MongoTemplate mongoTemplate;
    @Resource
    TradingUnitOrgMapper tradingUnitOrgMapper;
    @Resource
    DeptMapper deptMapper;

    @Override
    public boolean execute(SocketPacket dto) {
        try {
            TenantUtils.execute(dto.getTenantId(), () -> {
                // 解析JSON字符串
                String orgCode = dto.getOrgCode().toUpperCase();
                JSONObject rootObj = JSONUtil.parseObj(dto.getData());
                // 获取交易平台的合同ID（根节点键名）
                String sysContractId = rootObj.keySet().iterator().next();
                // 根据合同名称获取合同信息是否存在
                ContractsDO contractsDO = contractsMapper.selectByContractId(sysContractId);
                if (ObjectUtil.isNotEmpty(contractsDO)){
                    JSONObject contractObj = rootObj.getJSONObject(sysContractId);
                    // 获取powerCurve节点
                    JSONObject powerCurveObj = contractObj.getJSONObject("powerCurve");
                    // 解析数据列表
                    JSONObject dataObj = powerCurveObj.getJSONObject("data");
                    int startRow = (int)dataObj.get("startRow");
                    JSONArray listArray = dataObj.getJSONArray("list");
                    List<ContractsPowerCurveDO> contractsPowerCurveDOList = new ArrayList<>();
                    Long tenantId = dto.getTenantId();
                    TradingUnitOrgDO tradingUnitOrgDO = tradingUnitOrgMapper.selectOne(new QueryWrapper<TradingUnitOrgDO>().eq("code", orgCode));
                    DeptDO rootDeptDO = deptMapper.selectOne(new QueryWrapper<DeptDO>().eq("tenant_id", tenantId).eq("parent_id", 0));
                    List<Long> rowNoList = new ArrayList<>();
                    List<ConstructsPowerCurvePointPriceDO> constructsZjnList = new ArrayList<>();
                    for (Object item : listArray) {
                        JSONObject recordObj = (JSONObject) item;
                        // 电力曲线所有点
                        StringBuilder allSalePoint = new StringBuilder("");
                        for (int i = 1; i <= 96; i++) {
                            String pointKey = "salePoint" + i;
                            String salePoint = recordObj.getStr(pointKey);
                            allSalePoint.append(convertScientificNumber(salePoint, true));
                            if (i!=96){
                                allSalePoint.append(",");
                            }
                        }
                        // 电价曲线所有点
                        StringBuilder allSalePrice = new StringBuilder("");
                        for (int i = 1; i <= 96; i++) {
                            String pointKey = "salePrice" + i;
                            String salePrice = recordObj.getStr(pointKey);
                            allSalePrice.append(convertScientificNumber(salePrice, true));
                            if (i!=96){
                                allSalePrice.append(",");
                            }
                        }

                        // 封装mysql的电力曲线数据集合
                        ContractsPowerCurveDO contractsPowerCurveDO = new ContractsPowerCurveDO();    // 时间信息
                        Long rowNoLong = Long.parseLong(startRow+"");
                        contractsPowerCurveDO.setRowNo(rowNoLong);
                        rowNoList.add(rowNoLong);

                        // 曲线类型
                        contractsPowerCurveDO.setCurveType(Global.CONTRACT_POWERCURVETYPE.get(recordObj.getStr("powerCurveType")));
                        // 主体信息
                        contractsPowerCurveDO.setMemberName(recordObj.getStr("generatorName"));
                        // 开始日期
                        contractsPowerCurveDO.setBeginTime(DateUtil.parseLocalDateTime(recordObj.getStr("startTime")));
                        // 结束日期
                        contractsPowerCurveDO.setEndTime(DateUtil.parseLocalDateTime(recordObj.getStr("endTime")));
                        // 运行时间
                        contractsPowerCurveDO.setRunDate(DateUtil.parseLocalDateTime(recordObj.getStr("genDate")));
                        // 日电量
                        contractsPowerCurveDO.setQuantity(new BigDecimal(recordObj.getStr("energy")));
                        // 曲线点数
                        contractsPowerCurveDO.setPoints(recordObj.getStr("powerNo"));
                        // 现货系统的合同ID
                        contractsPowerCurveDO.setContractsId(String.valueOf(contractsDO.getId()));
                        // 租户ID
                        contractsPowerCurveDO.setTenantId(tenantId);
                        // 上级机构唯一编码(爬数用的交易单元号)
                        contractsPowerCurveDO.setOrgCode(orgCode);
                        // 部门ID
                        contractsPowerCurveDO.setDeptId(Long.valueOf(tradingUnitOrgDO.getDeptId()));
                        // 爬取时间
                        contractsPowerCurveDO.setGatherDate(DateUtil.toLocalDateTime(dto.getGatherTime()));
                        contractsPowerCurveDOList.add(contractsPowerCurveDO);

                        // 封装电力、电价曲线集合到mongo
                        ConstructsPowerCurvePointPriceDO constructsZjn = new ConstructsPowerCurvePointPriceDO();
                        constructsZjn.setRowNo(rowNoLong);
                        constructsZjn.setTenantId(tenantId);
                        constructsZjn.setOrgCode(tradingUnitOrgDO.getOrgCode());
                        constructsZjn.setContractsId(String.valueOf(contractsDO.getId()));
                        constructsZjn.setTradingUnitId(orgCode);
                        constructsZjn.setPowerCurve(allSalePoint.toString());
                        constructsZjn.setPriceCurve(allSalePrice.toString());
                        constructsZjn.setSysContractId(contractsDO.getSysContractId());
                        constructsZjnList.add(constructsZjn);

                        startRow++;
                    }

                    // 保存电力曲线列表到mysql库中
                    if (!contractsPowerCurveDOList.isEmpty()) {
                        contractsPowerCurveMapper.deleteByRowNoList(contractsDO.getId(),rowNoList);
                        contractsPowerCurveMapper.insertBatch(contractsPowerCurveDOList);
                        log.info("合同名称："+contractsDO.getName()+",startRow:"+dataObj.get("startRow")+"，保存电力曲线到mysql成功");
                    }
                    // 保存电力曲线和电价曲线到mongo库中
                    if (!constructsZjnList.isEmpty()){
                        try {
                            List<Query> queryList = new ArrayList<>();
                            BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, "constructs_"+rootDeptDO.getOrgCode().toLowerCase());
                            rowNoList.forEach(rowNo -> {
                                Query query = new Query(new Criteria("rowNo").is(rowNo).and("contractsId").is(String.valueOf(contractsDO.getId())));
                                queryList.add(query);
                            });
                            operations.remove(queryList);
                            operations.execute();
                            mongoTemplate.insert(constructsZjnList,"constructs_"+rootDeptDO.getOrgCode().toLowerCase());
                            log.info("合同名称："+contractsDO.getName()+",startRow:"+dataObj.get("startRow")+"，保存电力曲线到mongo成功");
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
            });
            return true;
        } catch (Exception ex) {
            log.error(StrUtil.format("[{}] > 解析出现异常", dto.getBizCode()), ex);
            return false;
        }
    }

    /**
     * 安全转换科学计数法为数值字符串
     * @param numberStr 输入字符串
     * @param forcePlain 是否强制转换为普通表示法
     * @return 转换后的字符串
     */
    public static String convertScientificNumber(String numberStr, boolean forcePlain) {
        if (numberStr == null) {
            return null;
        }

        try {
            // 先转换为BigDecimal保证精度
            BigDecimal bd = new BigDecimal(numberStr);

            // 特殊处理0的科学计数法表示
            if (bd.compareTo(BigDecimal.ZERO) == 0 && numberStr.toUpperCase().contains("E")) {
                return "0";
            }

            // 如果需要强制普通表示法
            if (forcePlain) {
                return bd.toPlainString();
            }

            return numberStr;
        } catch (NumberFormatException e) {
            return numberStr; // 不是有效数字，返回原字符串
        }
    }

    /**
     * 判断字符串是否是科学计数法
     */
    public static boolean isScientificNotation(String numberStr) {
        return numberStr != null && numberStr.toUpperCase().matches(".*[E].*");
    }

    public static void main(String[] args) {
        String[] numbers = {"1.23E4", "0E-8", "3.14E-2", "12345", "abc","null"};

        for (String num : numbers) {
            System.out.println(num + " -> " + convertScientificNumber(num, true) +
                    " (科学计数法? " + isScientificNotation(num) + ")");
        }
    }
}
