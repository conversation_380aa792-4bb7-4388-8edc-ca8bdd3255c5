package cn.iocoder.yudao.module.et.service.tradingunit;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.DispatchNodeDO;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingCalendar.TradingAnnouncementDO;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO;
import cn.iocoder.yudao.module.et.dal.mysql.dispatchnode.DispatchNodeMapper;
import cn.iocoder.yudao.module.et.dal.mysql.tradingCalendar.TradingAnnouncementMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.tradingunit.TradingUnitMapper;
import cn.iocoder.yudao.module.et.service.companyunit.CompanyUnitService;
import cn.iocoder.yudao.module.et.util.JyDateTimeUtil;
import cn.iocoder.yudao.module.et.util.MapSortUtils;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 交易单元 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TradingUnitServiceImpl implements TradingUnitService {

    @Resource
    private TradingUnitMapper tradingUnitMapper;
    @Resource
    private TradingAnnouncementMapper tradingAnnouncementMapper;
    @Resource
    private DispatchNodeMapper dispatchNodeMapper;
    @Resource
    private DeptApi deptApi;
    @Resource
    private CompanyUnitService companyUnitService;

    /**
     * 从给定的tradingUnitDOList中提取每15分钟的数据
     */
    public static List<TradingUnitDO> extractFifteenMinuteData(List<TradingUnitDO> tradingUnitDOList) {
        List<TradingUnitDO> filteredList = new ArrayList<>();

        // 过滤出每15分钟的数据
        for (TradingUnitDO unit : tradingUnitDOList) {
            long timestampMillis = unit.getTs().getTime();
            Instant instant = Instant.ofEpochMilli(timestampMillis);
            LocalDateTime timeOfDay = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
            LocalTime time = timeOfDay.toLocalTime();

            // 计算是否为15分钟的倍数
            int minutes = time.getMinute();
            if (minutes % 15 == 0) {
                filteredList.add(unit);
            }
        }

        return filteredList;
    }

    public static Map<String, List<TradingUnitDO>> groupDataByDate(List<TradingUnitDO> extractList) {
        Map<String, List<TradingUnitDO>> groupedData = new HashMap<>();
        // 分组数据
        for (TradingUnitDO unit : extractList) {
            long timestampMillis = unit.getTs().getTime();
            unit.setHisMarketSettlementPrices(BigDecimal.valueOf(unit.getHisMarketSettlementPrices()).setScale(2, RoundingMode.HALF_UP).floatValue());
            unit.setRealMarketSettlementPrices(BigDecimal.valueOf(unit.getRealMarketSettlementPrices()).setScale(2, RoundingMode.HALF_UP).floatValue());
            Instant instant = Instant.ofEpochMilli(timestampMillis);
            LocalDateTime timeOfDay = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
            String dateKey = timeOfDay.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
            groupedData.computeIfAbsent(dateKey, k -> new ArrayList<>()).add(unit);
        }
        return groupedData;
    }

    public static Map<String, List<TradingUnitDO>> sortMapByDate(Map<String, List<TradingUnitDO>> originalMap) {
        // 将原始的 Map 转换为一个包含日期字符串和对应列表的列表
        List<Map.Entry<String, List<TradingUnitDO>>> entryList = new ArrayList<>(originalMap.entrySet());

        // 使用自定义比较器对列表进行排序
        Collections.sort(entryList, new Comparator<Map.Entry<String, List<TradingUnitDO>>>() {
            @Override
            public int compare(Map.Entry<String, List<TradingUnitDO>> e1, Map.Entry<String, List<TradingUnitDO>> e2) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
                LocalDate date1 = LocalDate.parse(e1.getKey(), formatter);
                LocalDate date2 = LocalDate.parse(e2.getKey(), formatter);
                return date1.compareTo(date2);
            }
        });

        // 将排序后的列表转换回 Map
        return entryList.stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                (oldValue, newValue) -> oldValue, LinkedHashMap::new));
    }

    public static void main(String[] args) {

        // 假设你有一个特定的日期
        LocalDate specificDate = LocalDate.parse("2024-11-01"); // 请替换为你需要比较的日期
        LocalDate today = LocalDate.now(); // 获取当前日期

        // 计算两个日期之间的天数差异
        long daysBetween = ChronoUnit.DAYS.between(today, specificDate);

        // 输出结果
        System.out.println("日期 " + specificDate + " 距离今天的天数是: " + daysBetween);
    }

    private static boolean containsChinese(String str) {
        Pattern pattern = Pattern.compile("[\\u4E00-\\u9FA5]");
        return pattern.matcher(str).find();
    }

    /**
     * 日前或实时节点价格查询
     *
     * @param unitId    场站id
     * @param monthDate 月份
     * @param date      日期
     * @param realORHis 日前还是实时
     * @return
     */
    @Override
    public Map<String, Object> getRealTimePriceComparison(String unitId, String monthDate, Long[] date, String realORHis) {
        List<TradingUnitDO> tradingUnitDOList;
        Arrays.sort(date);
        if (date.length == 0) {
            monthDate = monthDate + "-01 00:00:00";
            Timestamp startDate = Timestamp.valueOf(monthDate);
            // 使用 Calendar 进行时间操作
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(startDate.getTime());
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            // 获取下个月 1 号的时间戳
            Timestamp endDate = new Timestamp(calendar.getTimeInMillis());
            tradingUnitDOList = tradingUnitMapper.getRealTimePriceComparisonByMonth(unitId, startDate, endDate);
        } else {
            Timestamp[] timestamps = new Timestamp[date.length + 1];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            timestamps[date.length] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[date.length - 1] + 60 * 60 * 24 * 1000L)));
            tradingUnitDOList = tradingUnitMapper.getRealTimePriceComparisonByDate(unitId, timestamps[0], timestamps[timestamps.length - 1]);
        }
        //提取出每15分钟的数据
        List<TradingUnitDO> extractList = extractFifteenMinuteData(tradingUnitDOList);
        //筛选  如果按照日查询可能跳着查, 在写sql的时候使用的是最早的时间和最晚的时间范围查, 需要吧中间多查出来的数据删掉
        List<TradingUnitDO> filteredData = new ArrayList<>();
        if (date.length == 0) {
            filteredData = extractList;
        } else {
            for (Long dayTimestamp : date) {
                long dayStart = dayTimestamp;
                long dayEnd = dayStart + 24 * 60 * 60 * 1000 - 1000;
                filteredData.addAll(extractList.stream().filter(unit -> unit.getTs().getTime() >= dayStart && unit.getTs().getTime() < dayEnd).toList());
            }
        }
        //根据数据分割出每天的数据     00:15-00:00
        //先根据时间分组然后排序
        Map<String, List<TradingUnitDO>> result = sortMapByDate(groupDataByDate(filteredData));
        Map<String, Double> groupByHour;
        if (realORHis.equals("实时")) {
            groupByHour = filteredData.stream().collect(Collectors.groupingBy(x -> DateUtil.format(new Date(x.getTs().getTime()), "HH"), Collectors.summingDouble(TradingUnitDO::getRealMarketSettlementPrices)));
        } else {
            groupByHour = filteredData.stream().collect(Collectors.groupingBy(x -> DateUtil.format(new Date(x.getTs().getTime()), "HH"), Collectors.summingDouble(TradingUnitDO::getHisMarketSettlementPrices)));
        }
        //排序
        Map<Integer, Object> sortedMap = new TreeMap<>();
        for (Map.Entry<String, Double> entry : groupByHour.entrySet()) {
            int key = Integer.parseInt(entry.getKey()) + 1;
            sortedMap.put(key, BigDecimal.valueOf(entry.getValue()).setScale(2, RoundingMode.HALF_UP));
        }
        for (Map.Entry<String, Double> entry : groupByHour.entrySet()) {
            int key = Integer.parseInt(entry.getKey()) + 1;
            sortedMap.put(key, new BigDecimal(sortedMap.get(key).toString()).divide(BigDecimal.valueOf(4 * (filteredData.size() / 96)), 2, RoundingMode.HALF_UP).doubleValue());
        }
        Map<String, Object> map = new HashMap<>();
        map.put("table", sortedMap);
        map.put("echarts", result);
        return map;
    }

    /**
     * 日前实时价格对比
     *
     * @param unitId
     * @param monthDate
     * @param date
     * @return
     */
    @Override
    public Map<String, Object> getRealTimePriceCompare(String unitId, String monthDate, Long[] date) {
        List<TradingUnitDO> tradingUnitDOList;
        Arrays.sort(date);
        if (date.length == 0) {
            monthDate = monthDate + "-01 00:00:00";
            Timestamp startDate = Timestamp.valueOf(monthDate);
            // 使用 Calendar 进行时间操作
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(startDate.getTime());
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            // 获取下个月 1 号的时间戳
            Timestamp endDate = new Timestamp(calendar.getTimeInMillis());
            tradingUnitDOList = tradingUnitMapper.getRealTimePriceComparisonByMonth(unitId, startDate, endDate);
        } else {
            Timestamp[] timestamps = new Timestamp[date.length + 1];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            timestamps[date.length] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[date.length - 1] + 60 * 60 * 24 * 1000L)));
            tradingUnitDOList = tradingUnitMapper.getRealTimePriceComparisonByDate(unitId, timestamps[0], timestamps[timestamps.length - 1]);
        }
        //提取出每15分钟的数据
        List<TradingUnitDO> extractList = extractFifteenMinuteData(tradingUnitDOList);
        //筛选  如果按照日查询可能跳着查, 在写sql的时候使用的是最早的时间和最晚的时间范围查, 需要吧中间多查出来的数据删掉
        List<TradingUnitDO> filteredData = new ArrayList<>();
        if (date.length == 0) {
            filteredData = extractList;
        } else {
            for (Long dayTimestamp : date) {
                long dayStart = dayTimestamp;
                long dayEnd = dayStart + 24 * 60 * 60 * 1000 - 100;
                filteredData.addAll(extractList.stream().filter(unit -> unit.getTs().getTime() >= dayStart && unit.getTs().getTime() < dayEnd).toList());
            }
        }
        //filteredData根据月/日分组 每天一个平均值
        Map<String, List<TradingUnitDO>> result = sortMapByDate(groupDataByDate(filteredData));
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        List<String> xName = new ArrayList<>();
        List<Double> hisDay = new ArrayList<>();
        List<Double> realDay = new ArrayList<>();
        for (String key : result.keySet()) {
            //2024年10月01日 转成10/01
            LocalDate date1 = LocalDate.parse(key, inputFormatter);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("MM/dd");
            String formattedDate = date1.format(outputFormatter);
            xName.add(formattedDate);
            BigDecimal hisAvg = BigDecimal.valueOf(result.get(key).stream().mapToDouble(TradingUnitDO::getRealMarketSettlementPrices).average().orElse(0));
            hisAvg = hisAvg.setScale(2, RoundingMode.HALF_UP);
            BigDecimal realAvg = BigDecimal.valueOf(result.get(key).stream().mapToDouble(TradingUnitDO::getHisMarketSettlementPrices).average().orElse(0));
            realAvg = realAvg.setScale(2, RoundingMode.HALF_UP);
            hisDay.add(hisAvg.doubleValue());
            realDay.add(realAvg.doubleValue());
        }
        //filteredData获取所有数据0点0的平均值0点15的平均值依次类推到23点45的平均值 一共96个平均值
        List<Double> hisAvg = new ArrayList<>();
        List<Double> realAvg = new ArrayList<>();
        for (int i = 0; i < 96; i++) {
            int count = 0;
            double hisSum = 0;
            double realSum = 0;
            for (TradingUnitDO unit : filteredData) {
                long ts = unit.getTs().getTime();
                int timeSlot = JyDateTimeUtil.getTimeSlot(ts);
                if (timeSlot == i) {
                    hisSum += unit.getRealMarketSettlementPrices();
                    realSum += unit.getHisMarketSettlementPrices();
                    count++;
                }
            }
            double average = count > 0 ? hisSum / count : 0;
            double average1 = count > 0 ? realSum / count : 0;
            BigDecimal avg = new BigDecimal(average);
            avg = avg.setScale(2, RoundingMode.HALF_UP);
            BigDecimal avg1 = new BigDecimal(average1);
            avg1 = avg1.setScale(2, RoundingMode.HALF_UP);
            hisAvg.add(avg.doubleValue());
            realAvg.add(avg1.doubleValue());
        }
        Map<String, Object> map = new HashMap<>();
        map.put("xName", xName);
        map.put("hisDay", hisDay);
        map.put("realDay", realDay);
        map.put("hisAvg", hisAvg);
        map.put("realAvg", realAvg);
        return map;
    }

    /**
     * 交易复盘-交易概览
     *
     * @param ids
     * @param date
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    @Override
    public Map<String, Object> getTransactionOverview(String[] ids, Long[] date) {
        List<List<Object>> dlList = new ArrayList<>();
        List<List<Object>> dfList = new ArrayList<>();
        //中长期电量累计值
        BigDecimal zcqdl = BigDecimal.ZERO;
        //日前电量累计值
        BigDecimal rqdl = BigDecimal.ZERO;
        //实时电量累计值
        BigDecimal ssdl = BigDecimal.ZERO;
        //中长期电费累计
        BigDecimal zcqdf = BigDecimal.ZERO;
        //日前电费累计
        BigDecimal rqdf = BigDecimal.ZERO;
        //实时电费累计
        BigDecimal ssdf = BigDecimal.ZERO;
        //度电收益=(中长期电费累计+日前电费累计+实时电费累计)/(中长期电量累计+日前电量累计+实时电量累计)
        BigDecimal ddsy = BigDecimal.ZERO;
        date[1] = date[1] + 86400000L - 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
        }
        List<TradingUnitDO> tradingUnitDOList = new ArrayList<>();
        for (String id : ids) {
            List<TradingUnitDO> tradingUnitDOS = tradingUnitMapper.getTransactionOverview(id, timestamps[0], timestamps[timestamps.length - 1]);
            tradingUnitDOList.addAll(tradingUnitDOS);
        }
        if (!tradingUnitDOList.isEmpty()) {
            //根据时分分组
            Map<String, List<TradingUnitDO>> groupedMap = tradingUnitDOList.stream()
                    .collect(Collectors.groupingBy(
                            tradingUnitDO -> {
                                LocalDateTime localDateTime = tradingUnitDO.getTs().toLocalDateTime();
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
                                return localDateTime.format(formatter);
                            }
                    ));
            //排序 升序
            Map<String, List<TradingUnitDO>> sortedMap = groupedMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey(Comparator.naturalOrder()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
            //在拼接柱状图数据的时候累计下合计值
            //中长期电量累计值
            List<BigDecimal> zcqdllj = new ArrayList<>();
            //日前电量累计值
            List<BigDecimal> rqdllj = new ArrayList<>();
            //实时电量累计值
            List<BigDecimal> ssdllj = new ArrayList<>();
            //中长期电费累计
            List<BigDecimal> zcqdflj = new ArrayList<>();
            //日前电费累计
            List<BigDecimal> rqdflj = new ArrayList<>();
            //实时电费累计
            List<BigDecimal> ssdflj = new ArrayList<>();
            //拼接柱状图数据
            sortedMap.forEach((key, value) -> {
                List<Object> dl = new ArrayList<>();
                List<Object> df = new ArrayList<>();
                //电量
                List<Double> zcqdlzzt = value.stream().map(item -> BigDecimal.valueOf(item.getLongTermContractPower()).setScale(2, RoundingMode.HALF_UP).doubleValue()).toList();
                List<Double> sbdl = value.stream().map(item -> BigDecimal.valueOf(item.getBilateralPower()).setScale(2, RoundingMode.HALF_UP).doubleValue()).toList();
                List<Double> wsdl = value.stream().map(item -> BigDecimal.valueOf(item.getDeliveryPower()).setScale(2, RoundingMode.HALF_UP).doubleValue()).toList();
                List<Double> rgddl = value.stream().map(item -> BigDecimal.valueOf(item.getDayScrollPower()).setScale(2, RoundingMode.HALF_UP).doubleValue()).toList();
                List<Double> rqdlzzt = value.stream().map(item -> BigDecimal.valueOf(item.getHisPositivePower()).setScale(2, RoundingMode.HALF_UP).doubleValue()).toList();
                List<Double> ssdlzzt = value.stream().map(item -> BigDecimal.valueOf(item.getRealPositivePower()).setScale(2, RoundingMode.HALF_UP).doubleValue()).toList();
                dl.add(key);
                dl.add(zcqdlzzt.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                zcqdllj.add(zcqdlzzt.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                dl.add(sbdl.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                dl.add(wsdl.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                dl.add(rgddl.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                dl.add(rqdlzzt.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                rqdllj.add(rqdlzzt.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                dl.add(ssdlzzt.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                ssdllj.add(ssdlzzt.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                //电费=电量*电价
                df.add(key);
                BigDecimal zcqdf1 = BigDecimal.ZERO;
                BigDecimal sbdf1 = BigDecimal.ZERO;
                BigDecimal wsdf1 = BigDecimal.ZERO;
                BigDecimal rgddf1 = BigDecimal.ZERO;
                BigDecimal rqdf1 = BigDecimal.ZERO;
                BigDecimal ssdf1 = BigDecimal.ZERO;
                for (TradingUnitDO tradingUnitDO : value) {
                    zcqdf1 = zcqdf1.add(BigDecimal.valueOf(tradingUnitDO.getLongTermContractPower()).multiply(BigDecimal.valueOf(tradingUnitDO.getLongTermContractPrice())).setScale(2, RoundingMode.HALF_UP));
                    sbdf1 = sbdf1.add(BigDecimal.valueOf(tradingUnitDO.getBilateralPower()).multiply(BigDecimal.valueOf(tradingUnitDO.getBilateralPrice())).setScale(2, RoundingMode.HALF_UP));
                    wsdf1 = wsdf1.add(BigDecimal.valueOf(tradingUnitDO.getDeliveryPower()).multiply(BigDecimal.valueOf(tradingUnitDO.getDeliveryPrice())).setScale(2, RoundingMode.HALF_UP));
                    rgddf1 = rgddf1.add(BigDecimal.valueOf(tradingUnitDO.getDayScrollPower()).multiply(BigDecimal.valueOf(tradingUnitDO.getDayScrollPrice())).setScale(2, RoundingMode.HALF_UP));
                    rqdf1 = rqdf1.add(BigDecimal.valueOf(tradingUnitDO.getHisPositivePower()).multiply(BigDecimal.valueOf(tradingUnitDO.getHisPositivePrices())).setScale(2, RoundingMode.HALF_UP));
                    ssdf1 = ssdf1.add(BigDecimal.valueOf(tradingUnitDO.getRealPositivePower()).multiply(BigDecimal.valueOf(tradingUnitDO.getRealPositivePrices())).setScale(2, RoundingMode.HALF_UP));
                    zcqdflj.add(BigDecimal.valueOf(tradingUnitDO.getLongTermContractPower()).multiply(BigDecimal.valueOf(tradingUnitDO.getLongTermContractPrice())).setScale(2, RoundingMode.HALF_UP));
                    rqdflj.add(BigDecimal.valueOf(tradingUnitDO.getHisPositivePower()).multiply(BigDecimal.valueOf(tradingUnitDO.getHisPositivePrices())).setScale(2, RoundingMode.HALF_UP));
                    ssdflj.add(BigDecimal.valueOf(tradingUnitDO.getRealPositivePower()).multiply(BigDecimal.valueOf(tradingUnitDO.getRealPositivePrices())).setScale(2, RoundingMode.HALF_UP));
                }
                df.add(zcqdf1);
                df.add(sbdf1);
                df.add(wsdf1);
                df.add(rgddf1);
                df.add(rqdf1);
                df.add(ssdf1);
                dlList.add(dl);
                dfList.add(df);
            });
            zcqdl = zcqdllj.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            rqdl = rqdllj.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            ssdl = ssdllj.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            zcqdf = zcqdflj.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            rqdf = rqdflj.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            ssdf = ssdflj.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            //(中长期电量累计+日前电量累计+实时电量累计)
            BigDecimal value = zcqdl.add(rqdl).add(ssdl);
            if (value.compareTo(BigDecimal.ZERO) != 0) {
                ddsy = (zcqdf.add(rqdf).add(ssdf)).divide((zcqdl.add(rqdl).add(ssdl)), 2, RoundingMode.HALF_UP);
            } else {
                ddsy = BigDecimal.ZERO;
            }
        }
        return Map.of("df", dfList,
                "dl", dlList,
                "zcqdl", zcqdl,
                "rqdl", rqdl,
                "ssdl", ssdl,
                "zcqdf", zcqdf.setScale(2, RoundingMode.HALF_UP),
                "rqdf", rqdf.setScale(2, RoundingMode.HALF_UP),
                "ssdf", ssdf.setScale(2, RoundingMode.HALF_UP),
                "ddsy", ddsy);
    }

    @Override
    public Map<String, Object> getPowerGeneration(String unitId, String monthDate, Long[] date) {
        List<TradingUnitDO> tradingUnitDOList;
        Arrays.sort(date);
        if (date.length == 0) {
            monthDate = monthDate + "-01 00:00:00";
            Timestamp startDate = Timestamp.valueOf(monthDate);
            // 使用 Calendar 进行时间操作
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(startDate.getTime());
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            // 获取下个月 1 号的时间戳
            Timestamp endDate = new Timestamp(calendar.getTimeInMillis());
            tradingUnitDOList = tradingUnitMapper.getPowerGenerationByMonth(unitId, startDate, endDate);
        } else {
            Timestamp[] timestamps = new Timestamp[date.length + 1];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            timestamps[date.length] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[date.length - 1] + 60 * 60 * 24 * 1000L)));
            tradingUnitDOList = tradingUnitMapper.getPowerGenerationByDate(unitId, timestamps[0], timestamps[timestamps.length - 1]);
        }
        //提取出每15分钟的数据
        List<TradingUnitDO> extractList = extractFifteenMinuteData(tradingUnitDOList);
        //筛选  如果按照日查询可能跳着查, 在写sql的时候使用的是最早的时间和最晚的时间范围查, 需要吧中间多查出来的数据删掉
        List<TradingUnitDO> filteredData = new ArrayList<>();
        if (date.length == 0) {
            filteredData = extractList;
        } else {
            for (Long dayTimestamp : date) {
                long dayStart = dayTimestamp;
                long dayEnd = dayStart + 24 * 60 * 60 * 1000 - 100;
                filteredData.addAll(extractList.stream().filter(unit -> unit.getTs().getTime() >= dayStart && unit.getTs().getTime() < dayEnd).toList());
            }
        }
        //按月 group分组 组合日数据曲线
        Map<String, List<TradingUnitDO>> tradingUnitDOListMonthMap = filteredData.stream().collect(Collectors.groupingBy(x -> JyDateTimeUtil.getFormatDateStrForDay(x.getTs().getTime())));
        //按时间正序排列
        tradingUnitDOListMonthMap = MapSortUtils.sortByKey(tradingUnitDOListMonthMap, false);
        List<String> dayTimeLine = new ArrayList<>();

        List<Double> daySumScadaPowerValueLine = new ArrayList<>();
        List<Double> daySumTmrPowerValueLine = new ArrayList<>();
        List<Double> daySumSettlementPowerPowerValueLine = new ArrayList<>();

        Map<String, List<Float>> dayValueMapLine = new HashMap<>();
        tradingUnitDOListMonthMap.forEach((k, v) -> {
            dayTimeLine.add(k);
            daySumScadaPowerValueLine.add(v.stream().mapToDouble(TradingUnitDO::getScadaPower).sum());
            daySumTmrPowerValueLine.add(v.stream().mapToDouble(TradingUnitDO::getTmrPower).sum());
            daySumSettlementPowerPowerValueLine.add(v.stream().mapToDouble(TradingUnitDO::getSettlementPower).sum());

            List<Float> dayScadaPowerValueLine = new ArrayList<>();
            List<Float> dayTmrPowerValueLine = new ArrayList<>();
            List<Float> daySettlementPowerPowerValueLine = new ArrayList<>();
            v.forEach(x -> {
                dayScadaPowerValueLine.add(x.getScadaPower());
                dayTmrPowerValueLine.add(x.getTmrPower());
                daySettlementPowerPowerValueLine.add(x.getSettlementPower());
            });
            dayValueMapLine.put(k + "_Scada电量", dayScadaPowerValueLine);
            dayValueMapLine.put(k + "_Tmr电量", dayTmrPowerValueLine);
            dayValueMapLine.put(k + "_结算电量", daySettlementPowerPowerValueLine);
        });
        Map<String, Object> map = new HashMap<>();
        map.put("dayTimeLine", dayTimeLine);
        map.put("daySumScadaPowerValueLine", daySumScadaPowerValueLine);
        map.put("daySumTmrPowerValueLine", daySumTmrPowerValueLine);
        map.put("daySumSettlementPowerPowerValueLine", daySumSettlementPowerPowerValueLine);
        map.put("dayValueMapLine", dayValueMapLine);
        return map;
    }

    /**
     * 交易日历
     *
     * @param unitId
     * @param monthDate
     * @return
     */
    @Override
    public Map<String, Object> getTradingCalendar(String unitId, String monthDate) {

        List<TradingAnnouncementDO> tradingAnnouncementDOList = tradingAnnouncementMapper.selectListByDateAndStr("%" + monthDate + "%", "%未开始%");

        Map<String, Object> resultMap = new HashMap<>();
        Map<String, List<Map<String, Object>>> tradingListMap = new HashMap<>();
        List<Map<String, Object>> todayTradingList = new ArrayList<>();
        List<Map<String, Object>> nextDaysTradingList = new ArrayList<>();
        tradingAnnouncementDOList.forEach(x -> {
            String dateBegin = x.getDateBegin().toString();
            String dateEnd = x.getDateEnd().toString();

            LocalDate startDate1 = LocalDateTime.parse(dateBegin).toLocalDate();
            LocalDate endDate1 = LocalDateTime.parse(dateEnd).toLocalDate();
            // 按天遍历日期
            for (LocalDate date = startDate1; date.isBefore(endDate1.plusDays(1)); date = date.plusDays(1)) {
                List<Map<String, Object>> tradingList = tradingListMap.computeIfAbsent(date.toString(), k -> new ArrayList<>());
                tradingList = tradingListMap.get(date.toString());
                Map<String, Object> tradingMap = new HashMap<>();
                List<Integer> typeStr = new ArrayList<>();
                if (x.getThermalPower() == 1) {
                    typeStr.add(0);
                }
                if (x.getGreenPower() == 1) {
                    typeStr.add(1);
                }
                if (x.getPowerSales() == 1) {
                    typeStr.add(2);
                }
                tradingMap.put("type", typeStr.toArray());
                tradingMap.put("info", x.getName());
                tradingList.add(tradingMap);


                // 当前日期
                LocalDate today = LocalDate.now();
                if (date.equals(today)) {
                    Map<String, Object> todayMap = new HashMap<>();
                    todayMap.put("info", x.getName());
                    todayTradingList.add(todayMap);
                }

                // 计算两个日期之间的天数差异
                long daysBetween = ChronoUnit.DAYS.between(today, date);
                //过滤出未来7天的数据
                if (daysBetween <= 7 && daysBetween > 0) {
                    Map<String, Object> nextDayMap = new HashMap<>();
                    nextDayMap.put("date", date.toString());
                    nextDayMap.put("info", x.getName());
                    nextDaysTradingList.add(nextDayMap);
                }
            }
        });


        List<Map<String, Object>> resultListMap = new ArrayList<>();
        tradingListMap.forEach((k, v) -> {
            Map<String, Object> strMap = new HashMap<>();
            strMap.put("date", k);
            strMap.put("content", v.toArray());
            resultListMap.add(strMap);


        });
        resultMap.put("calendar", resultListMap);
        resultMap.put("todayTradingList", todayTradingList);
        resultMap.put("nextDaysTradingList", nextDaysTradingList);
        return resultMap;
    }

    /**
     * @param ids
     * @param monthDate
     * @param date
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    @Override
    public Map<String, Object> getReviewAnalysis(String[] ids, String monthDate, Long[] date) {

        List<TradingUnitDO> tradingUnitDOList = new ArrayList<>();
        Arrays.sort(date);
        List<DispatchNodeDO> dispatchNodeDOList = dispatchNodeMapper.selectList();
        Map<String, Double> dispatchNodeCapMap = new HashMap<>();
        dispatchNodeDOList.forEach(x -> {
            dispatchNodeCapMap.put(x.getCode(), x.getCapacity());
        });
        //所属集团code
        String orgCode = companyUnitService.getCurrentUserOrgCode();
        List<String> filteredList = Arrays.stream(ids)
                .filter(id -> id.contains(orgCode))
                .toList();
        if (date.length == 0) {
            monthDate = monthDate + "-01 00:00:00";
            Timestamp startDate = Timestamp.valueOf(monthDate);
            // 使用 Calendar 进行时间操作
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(startDate.getTime());
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            // 获取下个月 1 号的时间戳
            Timestamp endDate = new Timestamp(calendar.getTimeInMillis());

            for (String id : filteredList) {
                List<TradingUnitDO> tradingUnitDOS = tradingUnitMapper.getReviewAnalysisByMonth(id, startDate, endDate);
                tradingUnitDOList.addAll(tradingUnitDOS);
            }
        } else {
            Timestamp[] timestamps = new Timestamp[date.length + 1];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            timestamps[date.length] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[date.length - 1] + 60 * 60 * 24 * 1000L)));
            for (String id : filteredList) {
                List<TradingUnitDO> tradingUnitDOS = tradingUnitMapper.getReviewAnalysisByDay(id, timestamps[0], timestamps[timestamps.length - 1]);
                tradingUnitDOList.addAll(tradingUnitDOS);

            }
        }
        //提取出每15分钟的数据
        List<TradingUnitDO> extractList = extractFifteenMinuteData(tradingUnitDOList);
        //筛选  如果按照日查询可能跳着查, 在写sql的时候使用的是最早的时间和最晚的时间范围查, 需要吧中间多查出来的数据删掉
        List<TradingUnitDO> filteredData = new ArrayList<>();
        if (date.length == 0) {
            filteredData = extractList;
        } else {
            for (Long dayTimestamp : date) {
                long dayStart = dayTimestamp;
                long dayEnd = dayStart + 24 * 60 * 60 * 1000 - 100;
                filteredData.addAll(extractList.stream().filter(unit -> unit.getTs().getTime() >= dayStart && unit.getTs().getTime() < dayEnd).toList());
            }
        }
        //按月 交易单元分组
        Map<String, List<TradingUnitDO>> tradingUnitDOListMonthMap = filteredData.stream().collect(Collectors.groupingBy(TradingUnitDO::getUnitId));
        //按时间正序排列
        tradingUnitDOListMonthMap = MapSortUtils.sortByKey(tradingUnitDOListMonthMap, false);

        final List<LinkedHashMap<String, String>> tmepMaps = new ArrayList<>();
        for (int i = 1; i <= 28; i++) {
            tmepMaps.add(new LinkedHashMap<>());
        }


        tradingUnitDOListMonthMap.forEach((k, v) -> {

            Double cap = dispatchNodeCapMap.get(k.toLowerCase());
            if (ids.length == 1) {
                if (date.length == 0) {

                    //日期空
                    Map<String, List<TradingUnitDO>> approveMap = v.stream().collect(Collectors.groupingBy(x -> JyDateTimeUtil.getFormatDateStrForDay(x.getTs().getTime())));

                    //按时间正序排列
                    approveMap = MapSortUtils.sortByKey(approveMap, false);

                    approveMap.forEach((k1, v1) -> {
                        String timeL = DateUtil.parse(k1).getTime() + "";
                        tmepMaps.get(0).put(timeL, String.format("%.2f", cap));

                        setTempMapValue(timeL, v1, tmepMaps);
                    });
                    for (int i = 1; i <= 28; i++) {
                        setSummary(tmepMaps);
                    }
                } else {

                    //日期空
                    Map<String, List<TradingUnitDO>> approveMap = v.stream().collect(Collectors.groupingBy(x -> JyDateTimeUtil.getFormatDateStrForHour(x.getTs().getTime())));

                    //按时间正序排列
                    approveMap = MapSortUtils.sortByKey(approveMap, false);


                    for (int i = 1; i <= 28; i++) {

                        tmepMaps.add(new LinkedHashMap<>());
                    }

                    approveMap.forEach((k1, v1) -> {
                        String timeL = Convert.toInt(k1) + "";
                        tmepMaps.get(0).put(timeL, String.format("%.2f", cap));
                        setTempMapValue(timeL, v1, tmepMaps);

                    });
                    for (int i = 1; i <= 28; i++) {
                        setSummary(tmepMaps);
                    }
                }

            } else {

                String timeL = k.toLowerCase();
                tmepMaps.get(0).put(timeL, String.format("%.2f", cap));
                setTempMapValue(timeL, v, tmepMaps);

                for (int i = 1; i <= 28; i++) {
                    setSummary(tmepMaps);
                }

            }

        });

        Map<String, Object> map = new HashMap<>();
        map.put("tmepMaps", tmepMaps);
        return map;
    }

    /**
     * 市场动态-日前价格
     *
     * @param ids
     * @param date                 时间
     * @param dataSelectTypeOption 0是日前价格 1是结算价格 2实时价格
     * @return
     */
    @Override
    public List<List<Object>> getCurrentPrices(String[] ids, Long[] date, String dataSelectTypeOption) {
        List<List<Object>> list = new ArrayList<>();
        if (date.length > 0) {
            List<DispatchNodeDO> dispatchNodeDOList = dispatchNodeMapper.selectList();
            date[1] = date[1] + 86400000L - 1000;
            Timestamp[] timestamps = new Timestamp[date.length];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            //所属集团code
            String orgCode = companyUnitService.getCurrentUserOrgCode();
            List<String> filteredList = Arrays.stream(ids)
                    .filter(id -> id.contains(orgCode))
                    .toList();
            for (String unitId : filteredList) {
                List<TradingUnitDO> tradingUnitDOList = tradingUnitMapper.getCurrentPrices(unitId, timestamps[0], timestamps[timestamps.length - 1]);
                List<Object> objects = new ArrayList<>();
                List<DispatchNodeDO> collect = dispatchNodeDOList.stream().filter(x -> x.getCode().equals(unitId)).toList();
                List<DeptRespDTO> deptRespDTO = deptApi.getByOrgCode(collect.get(0).getOrgCode());
                objects.add(deptRespDTO.get(0).getName());
                objects.add(collect.get(0).getName());
                if ("0".equals(dataSelectTypeOption)) {
                    objects.addAll(tradingUnitDOList.stream().map(x -> BigDecimal.valueOf(x.getHisSettlementPricesLimit()).setScale(2, RoundingMode.HALF_UP)).toList());
                } else if ("1".equals(dataSelectTypeOption)) {
                    objects.addAll(tradingUnitDOList.stream().map(x -> BigDecimal.valueOf(x.getHisMarketSettlementPrices()).setScale(2, RoundingMode.HALF_UP)).toList());
                } else {
                    objects.addAll(tradingUnitDOList.stream().map(x -> BigDecimal.valueOf(x.getRealSettlementPrices()).setScale(2, RoundingMode.HALF_UP)).toList());
                }
                list.add(objects);
            }
        }
        return list;
    }

    @Override
    public Map<String, Object> getSettlementRelease(String unitId, String monthDate) {
        monthDate = monthDate + "-01 00:00:00";
        Timestamp startDate = Timestamp.valueOf(monthDate);
        // 使用 Calendar 进行时间操作
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(startDate.getTime());
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 获取下个月 1 号的时间戳
        Timestamp endDate = new Timestamp(calendar.getTimeInMillis());
//his_positive_power,his_negative_power,real_positive_power,real_negative_power,his_positive_prices ,his_negative_prices,real_positive_power,real_negative_power
        List<TradingUnitDO> tradingUnitDOList = tradingUnitMapper.getSettlementRelease(unitId, startDate, endDate);
        //日期空
        Map<String, List<TradingUnitDO>> tradingUnitDOListMap = tradingUnitDOList.stream().collect(Collectors.groupingBy(x -> JyDateTimeUtil.getFormatDateStrForDay(x.getTs().getTime())));
        //按时间正序排列
        tradingUnitDOListMap = MapSortUtils.sortByKey(tradingUnitDOListMap, false);
        List<String> timeLine = new ArrayList<>();
        List<String> hisPositivePrices = new ArrayList<>();
        List<String> hisNegativePrices = new ArrayList<>();
        List<String> realPositivePrices = new ArrayList<>();
        List<String> realNegativePrices = new ArrayList<>();
        List<List<Object>> tablesList = new ArrayList<>();

        List<Object> tablesHuiZong = new ArrayList<>(9);

        tablesHuiZong.add("汇总");
        for (int i = 0; i < 9; i++) {
            tablesHuiZong.add(0);
        }

        Map<String, Object> map = new HashMap<>();
        tablesList.add(tablesHuiZong);
        tradingUnitDOListMap.forEach((k, v) -> {
            timeLine.add(k);
            Double hisPositivePricesSum = v.stream().mapToDouble(TradingUnitDO::getHisPositivePrices).sum();
            Double hisNegativePricesSum = v.stream().mapToDouble(TradingUnitDO::getHisNegativePrices).sum();
            Double realPositivePricesSum = v.stream().mapToDouble(TradingUnitDO::getRealPositivePrices).sum();
            Double realNegativePricesSum = v.stream().mapToDouble(TradingUnitDO::getRealNegativePrices).sum();
            hisPositivePrices.add(String.format("%.2f", hisPositivePricesSum));
            hisNegativePrices.add(String.format("%.2f", hisNegativePricesSum));
            realPositivePrices.add(String.format("%.2f", realPositivePricesSum));
            realNegativePrices.add(String.format("%.2f", realNegativePricesSum));

            Double hisPositivePowerSum = v.stream().mapToDouble(TradingUnitDO::getHisPositivePower).sum();
            Double hisNegativePowerSum = v.stream().mapToDouble(TradingUnitDO::getHisNegativePower).sum();
            Double realPositivePowerSum = v.stream().mapToDouble(TradingUnitDO::getRealPositivePower).sum();
            Double realNegativePowerSum = v.stream().mapToDouble(TradingUnitDO::getRealNegativePower).sum();
            List<Object> tables = new ArrayList<>();
            tables.add(k);
            tables.add(String.format("%.2f", hisPositivePowerSum));
            tables.add(String.format("%.2f", hisNegativePowerSum));
            tables.add(String.format("%.2f", hisPositivePricesSum));
            tables.add(String.format("%.2f", hisNegativePricesSum));

            tablesHuiZong.set(1, Convert.toDouble(tablesHuiZong.get(1), 0d) + hisPositivePowerSum);
            tablesHuiZong.set(2, Convert.toDouble(tablesHuiZong.get(2), 0d) + hisNegativePowerSum);
            tablesHuiZong.set(3, Convert.toDouble(tablesHuiZong.get(3), 0d) + hisPositivePricesSum);
            tablesHuiZong.set(4, Convert.toDouble(tablesHuiZong.get(4), 0d) + hisNegativePricesSum);

            tables.add(String.format("%.2f", realPositivePowerSum));
            tables.add(String.format("%.2f", realNegativePowerSum));
            tables.add(String.format("%.2f", realPositivePricesSum));
            tables.add(String.format("%.2f", realNegativePricesSum));
            tablesHuiZong.set(5, Convert.toDouble(tablesHuiZong.get(5), 0d) + realPositivePowerSum);
            tablesHuiZong.set(6, Convert.toDouble(tablesHuiZong.get(6), 0d) + realNegativePowerSum);
            tablesHuiZong.set(7, Convert.toDouble(tablesHuiZong.get(7), 0d) + realPositivePricesSum);
            tablesHuiZong.set(8, Convert.toDouble(tablesHuiZong.get(8), 0d) + realNegativePricesSum);
            tablesList.add(tables);
        });

        tablesHuiZong.set(1, String.format("%.2f", Convert.toDouble(tablesHuiZong.get(1), 0d)));
        tablesHuiZong.set(2, String.format("%.2f", Convert.toDouble(tablesHuiZong.get(2), 0d)));
        tablesHuiZong.set(3, String.format("%.2f", Convert.toDouble(tablesHuiZong.get(3), 0d)));
        tablesHuiZong.set(4, String.format("%.2f", Convert.toDouble(tablesHuiZong.get(4), 0d)));

        tablesHuiZong.set(5, String.format("%.2f", Convert.toDouble(tablesHuiZong.get(5), 0d)));
        tablesHuiZong.set(6, String.format("%.2f", Convert.toDouble(tablesHuiZong.get(6), 0d)));
        tablesHuiZong.set(7, String.format("%.2f", Convert.toDouble(tablesHuiZong.get(7), 0d)));
        tablesHuiZong.set(8, String.format("%.2f", Convert.toDouble(tablesHuiZong.get(8), 0d)));

        map.put("timeLine", timeLine);
        map.put("hisPositivePrices", hisPositivePrices);
        map.put("hisNegativePrices", hisNegativePrices);
        map.put("realPositivePrices", realPositivePrices);
        map.put("realNegativePrices", realNegativePrices);
        map.put("tablesList", tablesList);
        return map;
    }

    @Override
    public Map<String, Object> getSettlementElectricity(String unitId, Long[] date) {

        String dateStr = DateUtil.format(new Date(date[0]), "yyyy-MM-dd HH:mm:ss");
        Timestamp startDate = Timestamp.valueOf(dateStr);
        // 使用 Calendar 进行时间操作
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(startDate.getTime());
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        // 获取下个月 1 号的时间戳
        Timestamp endDate = new Timestamp(calendar.getTimeInMillis() - 1000);

        List<TradingUnitDO> tradingUnitDOS = tradingUnitMapper.getSettlementElectricity(unitId, startDate, endDate);
        List<List<Object>> resultTableList = new ArrayList<>();
        tradingUnitDOS.forEach(x -> {
            List<Object> tempList = new ArrayList<>();
            tempList.add(x.getTs().toLocalDateTime().getHour() + ":" + x.getTs().toLocalDateTime().getMinute());
            tempList.add(String.format("%.2f", ((x.getTmrPower() - x.getScadaPower()) / x.getScadaPower())));
            tempList.add(String.format("%.2f", x.getTmrPower()));
            tempList.add(String.format("%.2f", x.getScadaPower()));
            tempList.add(String.format("%.2f", x.getRealUsePower()));
            tempList.add(String.format("%.2f", x.getCalibrationPower()));
            tempList.add(String.format("%.2f", x.getSettlementPower()));
            resultTableList.add(tempList);
        });
        Map<String, Object> map = new HashMap<>();
        map.put("resultTableList", resultTableList);
        return map;
    }

    @Override
    public Map<String, Object> getSettlementReleaseSum(String unitId, String monthDate, Long[] date) {
        List<TradingUnitDO> tradingUnitDOList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        if (date.length == 0 || date[0] < 0) {
            monthDate = monthDate + "-01 00:00:00";
            Timestamp startDate = Timestamp.valueOf(monthDate);
            // 使用 Calendar 进行时间操作
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(startDate.getTime());
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            // 获取下个月 1 号的时间戳
            Timestamp endDate = new Timestamp(calendar.getTimeInMillis());

            tradingUnitDOList = tradingUnitMapper.getSettlementReleaseSum(unitId, startDate, endDate);
            Map<String, List<TradingUnitDO>> tradingUnitDOListMap = tradingUnitDOList.stream().collect(Collectors.groupingBy(x -> JyDateTimeUtil.getFormatDateStr3(x.getTs().getTime())));
            //按时间正序排列
            tradingUnitDOListMap = MapSortUtils.sortByKey(tradingUnitDOListMap, false);

            List<List<Object>> hisPricesTable = new ArrayList<>();
            List<List<Object>> planTable = new ArrayList<>();

            List<Object> hisPricesTableHuiZong = new ArrayList<>();
            hisPricesTableHuiZong.add("汇总");
            hisPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisPositivePower).sum()));
            hisPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisNegativePower).sum()));
            hisPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisPositivePrices).sum()));
            hisPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisNegativePrices).sum()));
            hisPricesTable.add(hisPricesTableHuiZong);
            List<Object> planTableHuiZong = new ArrayList<>();
            planTableHuiZong.add("汇总");
            planTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisGenerateElectricityPlan).sum()));
            planTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getMediumLongTermPlan).sum()));
            planTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisProvinceClearance).sum()));
            planTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisMarketSettlementPrices).average().orElse(0)));
            planTable.add(planTableHuiZong);
//=========
            List<List<Object>> realPricesTable = new ArrayList<>();
            List<List<Object>> realPlanTable = new ArrayList<>();
            List<Object> realPricesTableHuiZong = new ArrayList<>();
            realPricesTableHuiZong.add("汇总");
            realPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealPositivePower).sum()));
            realPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealNegativePower).sum()));
            realPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealPositivePrices).sum()));
            realPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealNegativePrices).sum()));
            realPricesTable.add(realPricesTableHuiZong);
            List<Object> realPlanTableHuiZong = new ArrayList<>();
            realPlanTableHuiZong.add("汇总");
            realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisGenerateElectricityPlan).sum()));
            realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealProvinceClearance).sum()));
            realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getCrossProvincialPeakLoadBalancingPlan).sum()));
            realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getCrossProvincialPeakLoadBalancingReturn).sum()));
            realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealSettlementPower).sum()));
            realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealMarketSettlementPrices).average().orElse(0)));
            realPlanTable.add(realPlanTableHuiZong);

            //=========
            List<List<Object>> marketOperationTable = new ArrayList<>();
            List<Object> marketOperationTableHuiZong = new ArrayList<>();
            marketOperationTableHuiZong.add("汇总");
            marketOperationTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getEmergencyInvokeStartUpCompensationPrices).sum()));
            marketOperationTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getThermalPowerStartUpCompensationPrices).sum()));
            marketOperationTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getDeviationReviewPrices).sum()));
            marketOperationTable.add(marketOperationTableHuiZong);


            //=========
            List<List<Object>> issuancePricesTable = new ArrayList<>();
            List<Object> issuancePricesTableHuiZong = new ArrayList<>();
            issuancePricesTableHuiZong.add("汇总");
            issuancePricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices).sum()));
            issuancePricesTable.add(issuancePricesTableHuiZong);
            //=========
            List<List<Object>> blockagePricesTable = new ArrayList<>();
            List<Object> blockagePricesTableHuiZong = new ArrayList<>();
            blockagePricesTableHuiZong.add("汇总");
            blockagePricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getMediumLongTermBlockagePrices).sum()));
            blockagePricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getBlockingRiskHedgingPrices).sum()));
            blockagePricesTable.add(blockagePricesTableHuiZong);


            //=========
            List<List<Object>> bcPricesTable = new ArrayList<>();
            List<Object> bbcPricesTableHuiZong = new ArrayList<>();
            bbcPricesTableHuiZong.add("汇总");
            bbcPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getModifiedCompensationPrices).sum()));
            bbcPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getNecessaryStartUpCompensationPrices).sum()));
            bcPricesTable.add(bbcPricesTableHuiZong);

            //=========
            List<List<Object>> huiZongTable = new ArrayList<>();
            List<Object> huiZongTableHuiZong = new ArrayList<>();
            huiZongTableHuiZong.add("汇总");
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisPositivePower).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisNegativePower).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisPositivePrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisNegativePrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisGenerateElectricityPlan).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getMediumLongTermPlan).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisProvinceClearance).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisMarketSettlementPrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealPositivePower).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealNegativePower).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealPositivePrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealNegativePrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealProvinceClearance).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getCrossProvincialPeakLoadBalancingPlan).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getCrossProvincialPeakLoadBalancingReturn).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealSettlementPower).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealMarketSettlementPrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getEmergencyInvokeStartUpCompensationPrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getThermalPowerStartUpCompensationPrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getDeviationReviewPrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisStandbyClearance).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealStandbyClearance).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getMediumLongTermBlockagePrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getBlockingRiskHedgingPrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getModifiedCompensationPrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getNecessaryStartUpCompensationPrices).sum()));
            huiZongTable.add(huiZongTableHuiZong);

            tradingUnitDOListMap.forEach((k, v) -> {
                List<Object> hisTempList = new ArrayList<>();
                hisTempList.add(k);
                hisTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getHisPositivePower).sum()));
                hisTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getHisNegativePower).sum()));
                hisTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getHisPositivePrices).sum()));
                hisTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getHisNegativePrices).sum()));
                hisPricesTable.add(hisTempList);

                List<Object> hisPlanTempList = new ArrayList<>();
                hisPlanTempList.add(k);
                hisPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getHisGenerateElectricityPlan).sum()));
                hisPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getMediumLongTermPlan).sum()));
                hisPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getHisProvinceClearance).sum()));
                hisPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getHisMarketSettlementPrices).average().orElse(0)));
                planTable.add(hisPlanTempList);


                List<Object> realTempList = new ArrayList<>();
                realTempList.add(k);
                realTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRealPositivePower).sum()));
                realTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRealNegativePower).sum()));
                realTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRealPositivePrices).sum()));
                realTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRealNegativePrices).sum()));
                realPricesTable.add(realTempList);

                List<Object> realPlanTempList = new ArrayList<>();
                realPlanTempList.add(k);
                realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getHisGenerateElectricityPlan).sum()));
                realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRealProvinceClearance).sum()));
                realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getCrossProvincialPeakLoadBalancingPlan).sum()));
                realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getCrossProvincialPeakLoadBalancingReturn).average().orElse(0)));
                realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRealSettlementPower).sum()));
                realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRealMarketSettlementPrices).average().orElse(0)));
                realPlanTable.add(realPlanTempList);


                List<Object> marketOperationTableTempList = new ArrayList<>();
                marketOperationTableTempList.add(k);
                marketOperationTableTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getEmergencyInvokeStartUpCompensationPrices).sum()));
                marketOperationTableTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getThermalPowerStartUpCompensationPrices).sum()));
                marketOperationTableTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getDeviationReviewPrices).sum()));
                marketOperationTable.add(marketOperationTableTempList);


                //=========
                List<Object> issuancePricesTableTempList = new ArrayList<>();
                issuancePricesTableTempList.add(k);
                issuancePricesTableTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices).sum()));
                issuancePricesTable.add(issuancePricesTableTempList);


                //=========
                List<Object> blockagePricesTableTempList = new ArrayList<>();
                blockagePricesTableTempList.add(k);
                blockagePricesTableTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getMediumLongTermBlockagePrices).sum()));
                blockagePricesTableTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getBlockingRiskHedgingPrices).sum()));
                blockagePricesTable.add(blockagePricesTableTempList);


                //=========
                List<Object> bbcPricesTableTableTempList = new ArrayList<>();
                bbcPricesTableTableTempList.add("汇总");
                bbcPricesTableTableTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getModifiedCompensationPrices).sum()));
                bbcPricesTableTableTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getNecessaryStartUpCompensationPrices).sum()));
                bcPricesTable.add(bbcPricesTableTableTempList);


                //=================
                List<Object> huiZongTableHuiZongTempList = new ArrayList<>();
                huiZongTableHuiZongTempList.add(k);
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getHisPositivePower).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getHisNegativePower).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getHisPositivePrices).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getHisNegativePrices).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getHisGenerateElectricityPlan).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getMediumLongTermPlan).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getHisProvinceClearance).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getHisMarketSettlementPrices).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRealPositivePower).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRealNegativePower).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRealPositivePrices).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRealNegativePrices).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRealProvinceClearance).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getCrossProvincialPeakLoadBalancingPlan).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getCrossProvincialPeakLoadBalancingReturn).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRealSettlementPower).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRealMarketSettlementPrices).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getEmergencyInvokeStartUpCompensationPrices).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getThermalPowerStartUpCompensationPrices).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getDeviationReviewPrices).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getHisStandbyClearance).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getRealStandbyClearance).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getMediumLongTermBlockagePrices).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getBlockingRiskHedgingPrices).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getModifiedCompensationPrices).sum()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(TradingUnitDO::getNecessaryStartUpCompensationPrices).sum()));
                huiZongTable.add(huiZongTableHuiZongTempList);
            });

            map.put("hisPricesTable", hisPricesTable);
            map.put("planTable", planTable);
            map.put("realPricesTable", realPricesTable);
            map.put("realPlanTable", realPlanTable);
            map.put("marketOperationTable", marketOperationTable);
            map.put("issuancePricesTable", issuancePricesTable);
            map.put("blockagePricesTable", blockagePricesTable);
            map.put("bcPricesTable", bcPricesTable);
            map.put("huiZongTable", huiZongTable);


        } else {
            String dateStr = DateUtil.format(new Date(date[0]), "yyyy-MM-dd HH:mm:ss");
            Timestamp startDate = Timestamp.valueOf(dateStr);
            // 使用 Calendar 进行时间操作
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(startDate.getTime());
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            // 获取下个月 1 号的时间戳
            Timestamp endDate = new Timestamp(calendar.getTimeInMillis() - 1000);

            tradingUnitDOList = tradingUnitMapper.getSettlementReleaseSum(unitId, startDate, endDate);
            List<List<Object>> hisPricesTable = new ArrayList<>();
            List<List<Object>> planTable = new ArrayList<>();

            List<Object> hisPricesTableHuiZong = new ArrayList<>();
            hisPricesTableHuiZong.add("汇总");
            hisPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisPositivePower).sum()));
            hisPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisNegativePower).sum()));
            hisPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisPositivePrices).sum()));
            hisPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisNegativePrices).sum()));
            hisPricesTable.add(hisPricesTableHuiZong);
            List<Object> planTableHuiZong = new ArrayList<>();
            planTableHuiZong.add("汇总");
            planTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisGenerateElectricityPlan).sum()));
            planTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getMediumLongTermPlan).sum()));
            planTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisProvinceClearance).sum()));
            planTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisMarketSettlementPrices).average().orElse(0)));
            planTable.add(planTableHuiZong);
            //=========
            List<List<Object>> realPricesTable = new ArrayList<>();
            List<List<Object>> realPlanTable = new ArrayList<>();
            List<Object> realPricesTableHuiZong = new ArrayList<>();
            realPricesTableHuiZong.add("汇总");
            realPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealPositivePower).sum()));
            realPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealNegativePower).sum()));
            realPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealPositivePrices).sum()));
            realPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealNegativePrices).sum()));
            realPricesTable.add(realPricesTableHuiZong);
            List<Object> realPlanTableHuiZong = new ArrayList<>();
            realPlanTableHuiZong.add("汇总");
            realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisGenerateElectricityPlan).sum()));
            realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealProvinceClearance).sum()));
            realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getCrossProvincialPeakLoadBalancingPlan).sum()));
            realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getCrossProvincialPeakLoadBalancingReturn).sum()));
            realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealSettlementPower).sum()));
            realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealMarketSettlementPrices).average().orElse(0)));
            realPlanTable.add(realPlanTableHuiZong);


            //=========
            List<List<Object>> marketOperationTable = new ArrayList<>();
            List<Object> marketOperationTableHuiZong = new ArrayList<>();
            marketOperationTableHuiZong.add("汇总");
            marketOperationTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getEmergencyInvokeStartUpCompensationPrices).sum()));
            marketOperationTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getThermalPowerStartUpCompensationPrices).sum()));
            marketOperationTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getDeviationReviewPrices).sum()));
            marketOperationTable.add(marketOperationTableHuiZong);


            //=========
            List<List<Object>> issuancePricesTable = new ArrayList<>();
            List<Object> issuancePricesTableHuiZong = new ArrayList<>();
            issuancePricesTableHuiZong.add("汇总");
            issuancePricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices).sum()));
            issuancePricesTable.add(issuancePricesTableHuiZong);


            //=========
            List<List<Object>> blockagePricesTable = new ArrayList<>();
            List<Object> blockagePricesTableHuiZong = new ArrayList<>();
            blockagePricesTableHuiZong.add("汇总");
            blockagePricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getMediumLongTermBlockagePrices).sum()));
            blockagePricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getBlockingRiskHedgingPrices).sum()));
            blockagePricesTable.add(blockagePricesTableHuiZong);

            //=========
            List<List<Object>> bcPricesTable = new ArrayList<>();
            List<Object> bbcPricesTableTableHuiZong = new ArrayList<>();
            bbcPricesTableTableHuiZong.add("汇总");
            bbcPricesTableTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getModifiedCompensationPrices).sum()));
            bbcPricesTableTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getNecessaryStartUpCompensationPrices).sum()));
            bcPricesTable.add(bbcPricesTableTableHuiZong);

            //=========
            List<List<Object>> huiZongTable = new ArrayList<>();
            List<Object> huiZongTableHuiZong = new ArrayList<>();
            huiZongTableHuiZong.add("汇总");
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisPositivePower).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisNegativePower).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisPositivePrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisNegativePrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisGenerateElectricityPlan).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getMediumLongTermPlan).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisProvinceClearance).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisMarketSettlementPrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealPositivePower).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealNegativePower).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealPositivePrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealNegativePrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealProvinceClearance).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getCrossProvincialPeakLoadBalancingPlan).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getCrossProvincialPeakLoadBalancingReturn).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealSettlementPower).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealMarketSettlementPrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getEmergencyInvokeStartUpCompensationPrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getThermalPowerStartUpCompensationPrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getDeviationReviewPrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getHisStandbyClearance).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getRealStandbyClearance).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getMediumLongTermBlockagePrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getBlockingRiskHedgingPrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getModifiedCompensationPrices).sum()));
            huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(TradingUnitDO::getNecessaryStartUpCompensationPrices).sum()));
            huiZongTable.add(huiZongTableHuiZong);

            tradingUnitDOList.forEach(v -> {
                List<Object> tempList = new ArrayList<>();
                String k = JyDateTimeUtil.getFormatDateStr3(v.getTs().getTime());
                tempList.add(k);
                tempList.add(String.format("%.2f", v.getHisPositivePower()));
                tempList.add(String.format("%.2f", v.getHisNegativePower()));
                tempList.add(String.format("%.2f", v.getHisPositivePrices()));
                tempList.add(String.format("%.2f", v.getHisNegativePrices()));
                hisPricesTable.add(tempList);

                List<Object> planTempList = new ArrayList<>();
                planTempList.add(k);
                planTempList.add(String.format("%.2f", v.getHisGenerateElectricityPlan()));
                planTempList.add(String.format("%.2f", v.getMediumLongTermPlan()));
                planTempList.add(String.format("%.2f", v.getHisProvinceClearance()));
                planTempList.add(String.format("%.2f", v.getHisMarketSettlementPrices()));
                planTable.add(planTempList);

                //=============
                List<Object> realTempList = new ArrayList<>();
                realTempList.add(k);
                realTempList.add(String.format("%.2f", v.getRealPositivePower()));
                realTempList.add(String.format("%.2f", v.getRealNegativePower()));
                realTempList.add(String.format("%.2f", v.getRealPositivePrices()));
                realTempList.add(String.format("%.2f", v.getRealNegativePrices()));
                realPricesTable.add(realTempList);

                List<Object> realPlanTempList = new ArrayList<>();
                realPlanTempList.add(k);
                realPlanTempList.add(String.format("%.2f", v.getHisGenerateElectricityPlan()));
                realPlanTempList.add(String.format("%.2f", v.getRealProvinceClearance()));
                realPlanTempList.add(String.format("%.2f", v.getCrossProvincialPeakLoadBalancingPlan()));
                realPlanTempList.add(String.format("%.2f", v.getCrossProvincialPeakLoadBalancingReturn()));
                realPlanTempList.add(String.format("%.2f", v.getRealSettlementPower()));
                realPlanTempList.add(String.format("%.2f", v.getRealMarketSettlementPrices()));
                realPlanTable.add(realPlanTempList);

                List<Object> marketOperationTableTempList = new ArrayList<>();
                marketOperationTableTempList.add(k);
                marketOperationTableTempList.add(String.format("%.2f", v.getEmergencyInvokeStartUpCompensationPrices()));
                marketOperationTableTempList.add(String.format("%.2f", v.getThermalPowerStartUpCompensationPrices()));
                marketOperationTableTempList.add(String.format("%.2f", v.getDeviationReviewPrices()));
                marketOperationTable.add(marketOperationTableTempList);

                List<Object> issuancePricesTableTempList = new ArrayList<>();
                issuancePricesTableTempList.add(k);
                issuancePricesTableTempList.add(String.format("%.2f", v.getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices()));
                issuancePricesTable.add(issuancePricesTableTempList);


                //=========
                List<Object> blockagePricesTableTempList = new ArrayList<>();
                blockagePricesTableTempList.add(k);
                blockagePricesTableTempList.add(String.format("%.2f", v.getMediumLongTermBlockagePrices()));
                blockagePricesTableTempList.add(String.format("%.2f", v.getBlockingRiskHedgingPrices()));
                blockagePricesTable.add(blockagePricesTableTempList);

                //=========
                List<Object> bcPricesTableTempList = new ArrayList<>();
                bcPricesTableTempList.add(k);
                bcPricesTableTempList.add(String.format("%.2f", v.getModifiedCompensationPrices()));
                bcPricesTableTempList.add(String.format("%.2f", v.getNecessaryStartUpCompensationPrices()));
                bcPricesTable.add(bcPricesTableTempList);

                //=================
                List<Object> huiZongTableHuiZongTempList = new ArrayList<>();
                huiZongTableHuiZongTempList.add(k);
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getHisPositivePower()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getHisNegativePower()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getHisPositivePrices()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getHisNegativePrices()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getHisGenerateElectricityPlan()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getMediumLongTermPlan()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getHisProvinceClearance()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getHisMarketSettlementPrices()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getRealPositivePower()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getRealNegativePower()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getRealPositivePrices()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getRealNegativePrices()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getRealProvinceClearance()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getCrossProvincialPeakLoadBalancingPlan()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getCrossProvincialPeakLoadBalancingReturn()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getRealSettlementPower()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getRealMarketSettlementPrices()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getEmergencyInvokeStartUpCompensationPrices()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getThermalPowerStartUpCompensationPrices()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getDeviationReviewPrices()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getHisStandbyClearance()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getRealStandbyClearance()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getMediumLongTermBlockagePrices()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getBlockingRiskHedgingPrices()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getModifiedCompensationPrices()));
                huiZongTableHuiZongTempList.add(String.format("%.2f", v.getNecessaryStartUpCompensationPrices()));
                huiZongTable.add(huiZongTableHuiZongTempList);
            });
            map.put("hisPricesTable", hisPricesTable);
            map.put("planTable", planTable);
            map.put("realPricesTable", realPricesTable);
            map.put("realPlanTable", realPlanTable);
            map.put("marketOperationTable", marketOperationTable);
            map.put("issuancePricesTable", issuancePricesTable);
            map.put("blockagePricesTable", blockagePricesTable);
            map.put("bcPricesTable", bcPricesTable);
            map.put("huiZongTable", huiZongTable);

        }


        return map;
    }

    /**
     * 市场动态 市场出清监视
     *
     * @param unitId
     * @param date
     * @return
     */
    @Override
    public Map<String, Object> getClearanceMonitor(String unitId, Long[] date) {
        Map<String, Object> map = new HashMap<>();
        if (date.length > 0) {
            date[1] = date[1] + 86400000L - 1000;
            Timestamp[] timestamps = new Timestamp[date.length];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            List<TradingUnitDO> tradingUnitDOList = tradingUnitMapper.getClearanceMonitor(unitId, timestamps[0], timestamps[timestamps.length - 1]);
            //中长期结算曲线
            List<Float> mediumLongTermSettlementCurves = tradingUnitDOList.stream().map(TradingUnitDO::getMediumLongTermSettlementCurves)
                    .map(item -> BigDecimal.valueOf(item).setScale(2, RoundingMode.HALF_UP).floatValue()).toList();
            map.put("mediumLongTermSettlementCurves", mediumLongTermSettlementCurves);
            //自计划
            List<Float> selfPlan = tradingUnitDOList.stream().map(TradingUnitDO::getSelfPlan)
                    .map(item -> BigDecimal.valueOf(item).setScale(2, RoundingMode.HALF_UP).floatValue()).toList();
            map.put("selfPlan", selfPlan);
            //预平衡计划
            List<Float> prebalancePlan = tradingUnitDOList.stream().map(TradingUnitDO::getPrebalancePlan)
                    .map(item -> BigDecimal.valueOf(item).setScale(2, RoundingMode.HALF_UP).floatValue()).toList();
            map.put("prebalancePlan", prebalancePlan);
            //可靠性计划
            List<Float> reliabilityPlan = tradingUnitDOList.stream().map(TradingUnitDO::getReliabilityPlan)
                    .map(item -> BigDecimal.valueOf(item).setScale(2, RoundingMode.HALF_UP).floatValue()).toList();
            map.put("reliabilityPlan", reliabilityPlan);
            //双边出清
            List<Float> bilateralClearance = tradingUnitDOList.stream().map(TradingUnitDO::getBilateralClearance)
                    .map(item -> BigDecimal.valueOf(item).setScale(2, RoundingMode.HALF_UP).floatValue()).toList();
            map.put("bilateralClearance", bilateralClearance);
            //实时计划
            List<Float> realPlan = tradingUnitDOList.stream().map(TradingUnitDO::getRealPlan)
                    .map(item -> BigDecimal.valueOf(item).setScale(2, RoundingMode.HALF_UP).floatValue()).toList();
            map.put("realPlan", realPlan);
            //实时上网scada电力
            List<Float> realScadaPower = tradingUnitDOList.stream().map(TradingUnitDO::getRealScadaPower)
                    .map(item -> BigDecimal.valueOf(item).setScale(2, RoundingMode.HALF_UP).floatValue()).toList();
            map.put("realScadaPower", realScadaPower);
            //短期预测
            List<Float> shortTermForecast = tradingUnitDOList.stream().map(TradingUnitDO::getShortTermForecast)
                    .map(item -> BigDecimal.valueOf(item).setScale(2, RoundingMode.HALF_UP).floatValue()).toList();
            map.put("shortTermForecast", shortTermForecast);
            //超短期预测
            List<Float> ultraShortTermForecast = tradingUnitDOList.stream().map(TradingUnitDO::getUltraShortTermForecast)
                    .map(item -> BigDecimal.valueOf(item).setScale(2, RoundingMode.HALF_UP).floatValue()).toList();
            map.put("ultraShortTermForecast", ultraShortTermForecast);
            //跨省区现货出清电力
            List<Float> crossProvincialSpotClearingOfElectricity = tradingUnitDOList.stream().map(TradingUnitDO::getCrossProvincialSpotClearingOfElectricity)
                    .map(item -> BigDecimal.valueOf(item).setScale(2, RoundingMode.HALF_UP).floatValue()).toList();
            map.put("crossProvincialSpotClearingOfElectricity", crossProvincialSpotClearingOfElectricity);
            //实时出清依据
            List<Float> realBasisClearance = tradingUnitDOList.stream().map(TradingUnitDO::getRealBasisClearance)
                    .map(item -> BigDecimal.valueOf(item).setScale(2, RoundingMode.HALF_UP).floatValue()).toList();
            map.put("realBasisClearance", realBasisClearance);
        }
        return map;
    }

    private void setTempMapValue(String timeL, List<TradingUnitDO> v1, List<LinkedHashMap<String, String>> tmepMaps) {
        tmepMaps.get(2).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getScadaPower).sum()));
        tmepMaps.get(3).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getShortTermForecastGen).sum()));

        tmepMaps.get(5).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getRadixPower).sum()));
        tmepMaps.get(6).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getDeliveryPower).sum()));
        tmepMaps.get(7).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getBilateralPower).sum()));
        tmepMaps.get(8).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getDayScrollPower).sum()));
        tmepMaps.get(9).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getLongTermContractPrice).sum()));
        tmepMaps.get(10).put(timeL, "0");
        tmepMaps.get(11).put(timeL, "0");

        tmepMaps.get(12).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getHisPositivePower).sum()
                + v1.stream().mapToDouble(TradingUnitDO::getHisNegativePower).sum()));
        tmepMaps.get(13).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getRealPositivePower).sum()
                + v1.stream().mapToDouble(TradingUnitDO::getRealNegativePower).sum()));
        tmepMaps.get(14).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getHisMarketSettlementPrices).average().orElse(0)));
        tmepMaps.get(15).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getRealMarketSettlementPrices).average().orElse(0)));
        double xianhuoZongYingKui = v1.stream().mapToDouble(TradingUnitDO::getRealPositivePrices).sum()
                + v1.stream().mapToDouble(TradingUnitDO::getRealNegativePrices).sum()
                + v1.stream().mapToDouble(TradingUnitDO::getHisPositivePrices).sum()
                + v1.stream().mapToDouble(TradingUnitDO::getHisNegativePrices).sum()
                - v1.stream().mapToDouble(TradingUnitDO::getDeviationReviewPrices).sum();

        tmepMaps.get(1).put(timeL, String.format("%.2f", xianhuoZongYingKui));
        tmepMaps.get(4).put(timeL, String.format("%.2f", xianhuoZongYingKui / v1.stream().mapToDouble(TradingUnitDO::getScadaPower).sum()));
        tmepMaps.get(16).put(timeL, String.format("%.2f", xianhuoZongYingKui));
        tmepMaps.get(17).put(timeL, String.format("%.2f", (xianhuoZongYingKui / (v1.stream().mapToDouble(TradingUnitDO::getHisPositivePower).sum()
                + v1.stream().mapToDouble(TradingUnitDO::getHisNegativePower).sum()
                + v1.stream().mapToDouble(TradingUnitDO::getRealPositivePower).sum()
                + v1.stream().mapToDouble(TradingUnitDO::getRealNegativePower).sum()))));

        tmepMaps.get(18).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getDeviationReviewPrices).sum()));
        tmepMaps.get(19).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices).sum()));
        tmepMaps.get(20).put(timeL, "0");

        tmepMaps.get(21).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getMediumLongTermBlockagePrices).sum()));
        tmepMaps.get(22).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getBlockingRiskHedgingPrices).sum()));
        tmepMaps.get(23).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getModifiedCompensationPrices).sum()));
        tmepMaps.get(24).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getNecessaryStartUpCompensationPrices).sum()));
        tmepMaps.get(25).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getEmergencyInvokeStartUpCompensationPrices).sum()));
        tmepMaps.get(26).put(timeL, String.format("%.2f", v1.stream().mapToDouble(TradingUnitDO::getThermalPowerStartUpCompensationPrices).sum()));
    }

    private void setSummary(List<LinkedHashMap<String, String>> tmepMaps) {
        List<String> list0 = new ArrayList<>(tmepMaps.get(0).values());
        tmepMaps.get(0).put("Summary", String.format("%.2f", list0.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list1 = new ArrayList<>(tmepMaps.get(1).values());
        tmepMaps.get(1).put("Summary", String.format("%.2f", list1.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list2 = new ArrayList<>(tmepMaps.get(2).values());
        tmepMaps.get(2).put("Summary", String.format("%.2f", list2.stream().mapToDouble(Convert::toDouble).sum()));

        List<String> list3 = new ArrayList<>(tmepMaps.get(3).values());
        tmepMaps.get(3).put("Summary", String.format("%.2f", list3.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list4 = new ArrayList<>(tmepMaps.get(4).values());
        tmepMaps.get(4).put("Summary", String.format("%.2f", list4.stream().mapToDouble(Convert::toDouble).average().orElse(0)));

        List<String> list5 = new ArrayList<>(tmepMaps.get(5).values());
        tmepMaps.get(5).put("Summary", String.format("%.2f", list5.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list6 = new ArrayList<>(tmepMaps.get(6).values());
        tmepMaps.get(6).put("Summary", String.format("%.2f", list6.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list7 = new ArrayList<>(tmepMaps.get(7).values());
        tmepMaps.get(7).put("Summary", String.format("%.2f", list7.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list8 = new ArrayList<>(tmepMaps.get(8).values());
        tmepMaps.get(8).put("Summary", String.format("%.2f", list8.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list9 = new ArrayList<>(tmepMaps.get(9).values());
        tmepMaps.get(9).put("Summary", String.format("%.2f", list9.stream().mapToDouble(Convert::toDouble).average().orElse(0)));
        List<String> list10 = new ArrayList<>(tmepMaps.get(10).values());
        tmepMaps.get(10).put("Summary", String.format("%.2f", list10.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list11 = new ArrayList<>(tmepMaps.get(11).values());
        tmepMaps.get(11).put("Summary", String.format("%.2f", list11.stream().mapToDouble(Convert::toDouble).average().orElse(0)));

        List<String> list12 = new ArrayList<>(tmepMaps.get(12).values());
        tmepMaps.get(12).put("Summary", String.format("%.2f", list12.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list13 = new ArrayList<>(tmepMaps.get(13).values());
        tmepMaps.get(13).put("Summary", String.format("%.2f", list13.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list14 = new ArrayList<>(tmepMaps.get(14).values());
        tmepMaps.get(14).put("Summary", String.format("%.2f", list14.stream().mapToDouble(Convert::toDouble).average().orElse(0)));
        List<String> list15 = new ArrayList<>(tmepMaps.get(15).values());
        tmepMaps.get(15).put("Summary", String.format("%.2f", list15.stream().mapToDouble(Convert::toDouble).average().orElse(0)));
        List<String> list16 = new ArrayList<>(tmepMaps.get(16).values());
        tmepMaps.get(16).put("Summary", String.format("%.2f", list16.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list17 = new ArrayList<>(tmepMaps.get(17).values());
        tmepMaps.get(17).put("Summary", String.format("%.2f", list17.stream().mapToDouble(Convert::toDouble).average().orElse(0)));

        List<String> list18 = new ArrayList<>(tmepMaps.get(18).values());
        tmepMaps.get(18).put("Summary", String.format("%.2f", list18.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list19 = new ArrayList<>(tmepMaps.get(19).values());
        tmepMaps.get(19).put("Summary", String.format("%.2f", list19.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list20 = new ArrayList<>(tmepMaps.get(20).values());
        tmepMaps.get(20).put("Summary", String.format("%.2f", list20.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list21 = new ArrayList<>(tmepMaps.get(21).values());
        tmepMaps.get(21).put("Summary", String.format("%.2f", list21.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list22 = new ArrayList<>(tmepMaps.get(22).values());
        tmepMaps.get(22).put("Summary", String.format("%.2f", list22.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list23 = new ArrayList<>(tmepMaps.get(23).values());
        tmepMaps.get(23).put("Summary", String.format("%.2f", list23.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list24 = new ArrayList<>(tmepMaps.get(24).values());
        tmepMaps.get(24).put("Summary", String.format("%.2f", list24.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list25 = new ArrayList<>(tmepMaps.get(25).values());
        tmepMaps.get(25).put("Summary", String.format("%.2f", list25.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list26 = new ArrayList<>(tmepMaps.get(26).values());
        tmepMaps.get(26).put("Summary", String.format("%.2f", list26.stream().mapToDouble(Convert::toDouble).sum()));
    }

    /**
     * 交易复盘-预测偏差分析
     *
     * @param date
     * @return
     */
    @Override
    public Map<String, List<Double>> getDeviation(String unitId, Long[] date) {
        date[1] = date[1] + 86400000L - 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
        }
        List<TradingUnitDO> tradingUnitDOList = tradingUnitMapper.getDeviation(unitId, timestamps[0], timestamps[timestamps.length - 1]);
        //实际发电量-预测发电量
        Map<String, List<Double>> map = new HashMap<>();
        List<Double> realPower = new ArrayList<>();
        List<Double> forecastPower = new ArrayList<>();
        List<Double> devseries = new ArrayList<>();
        List<Double> deviationRate = new ArrayList<>();
        for (TradingUnitDO tradingUnitDO : tradingUnitDOList) {
            BigDecimal real = BigDecimal.valueOf(tradingUnitDO.getScadaPower()).setScale(2, RoundingMode.HALF_UP);
            BigDecimal forecast = BigDecimal.valueOf(tradingUnitDO.getTmrPower()).setScale(2, RoundingMode.HALF_UP);
            realPower.add(real.doubleValue());
            forecastPower.add(forecast.doubleValue());
            devseries.add(real.subtract(forecast).doubleValue());
            deviationRate.add(BigDecimal.valueOf(tradingUnitDO.getDeviationRate()).setScale(2, RoundingMode.HALF_UP).doubleValue());
        }
        map.put("realPower", realPower);
        map.put("forecastPower", forecastPower);
        map.put("devseries", devseries);
        map.put("deviationRate", deviationRate);
        return map;
    }
}
