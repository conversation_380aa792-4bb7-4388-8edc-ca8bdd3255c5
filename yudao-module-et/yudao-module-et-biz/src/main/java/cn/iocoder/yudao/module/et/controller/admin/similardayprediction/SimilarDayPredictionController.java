package cn.iocoder.yudao.module.et.controller.admin.similardayprediction;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.et.controller.admin.similardayprediction.vo.SimilarDayPredictionPageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.similardayprediction.vo.SimilarDayPredictionRespVO;
import cn.iocoder.yudao.module.et.controller.admin.similardayprediction.vo.SimilarDayPredictionSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.similardayprediction.SimilarDayPredictionDO;
import cn.iocoder.yudao.module.et.service.similardayprediction.SimilarDayPredictionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 相似日表 ")
@RestController
@RequestMapping("/et/similar-day-prediction")
@Validated
public class SimilarDayPredictionController {

    @Resource
    private SimilarDayPredictionService similarDayPredictionService;

    @PostMapping("/getSimilarDay")
    @Operation(summary = "获取相似日日期 ")
    //@PreAuthorize("@ss.hasPermission('et:similar-day-prediction:query')")
    public CommonResult<List<SimilarDayPredictionDO>> getSimilarDay(@Valid @RequestBody SimilarDayPredictionSaveReqVO reqVO) {
        LocalDateTime forecastDay = Instant.ofEpochMilli(reqVO.getForecastDay()).atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
        return success(similarDayPredictionService.getSimilarDay(forecastDay));
    }

    @PostMapping("/create")
    @Operation(summary = "创建相似日表 ")
    @PreAuthorize("@ss.hasPermission('et:similar-day-prediction:create')")
    public CommonResult<Long> createSimilarDayPrediction(@Valid @RequestBody SimilarDayPredictionSaveReqVO createReqVO) {
        return success(similarDayPredictionService.createSimilarDayPrediction(createReqVO));
    }


    @PutMapping("/update")
    @Operation(summary = "更新相似日表")
    @PreAuthorize("@ss.hasPermission('et:similar-day-prediction:update')")
    public CommonResult<Boolean> updateSimilarDayPrediction(@Valid @RequestBody SimilarDayPredictionSaveReqVO updateReqVO) {
        similarDayPredictionService.updateSimilarDayPrediction(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除相似日表 ")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('et:similar-day-prediction:delete')")
    public CommonResult<Boolean> deleteSimilarDayPrediction(@RequestParam("id") Long id) {
        similarDayPredictionService.deleteSimilarDayPrediction(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得相似日表 ")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('et:similar-day-prediction:query')")
    public CommonResult<SimilarDayPredictionRespVO> getSimilarDayPrediction(@RequestParam("id") Long id) {
        SimilarDayPredictionDO similarDayPrediction = similarDayPredictionService.getSimilarDayPrediction(id);
        return success(BeanUtils.toBean(similarDayPrediction, SimilarDayPredictionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得相似日表 分页")
    @PreAuthorize("@ss.hasPermission('et:similar-day-prediction:query')")
    public CommonResult<PageResult<SimilarDayPredictionRespVO>> getSimilarDayPredictionPage(@Valid SimilarDayPredictionPageReqVO pageReqVO) {
        PageResult<SimilarDayPredictionDO> pageResult = similarDayPredictionService.getSimilarDayPredictionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SimilarDayPredictionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出相似日表 Excel")
    @PreAuthorize("@ss.hasPermission('et:similar-day-prediction:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSimilarDayPredictionExcel(@Valid SimilarDayPredictionPageReqVO pageReqVO,
                                                HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SimilarDayPredictionDO> list = similarDayPredictionService.getSimilarDayPredictionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "相似日表.xls", "数据", SimilarDayPredictionRespVO.class,
                BeanUtils.toBean(list, SimilarDayPredictionRespVO.class));
    }

}