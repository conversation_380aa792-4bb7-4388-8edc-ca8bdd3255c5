package cn.iocoder.yudao.module.et.controller.admin.startupanddownunitdetail;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.et.controller.admin.startupanddownunitdetail.vo.StartupAndDownUnitDetailRespVO;
import cn.iocoder.yudao.module.et.controller.admin.startupanddownunitdetail.vo.StartupAndDownUnitDetailSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.startupanddownunitdetail.StartupAndDownUnitDetailDO;
import cn.iocoder.yudao.module.et.service.startupanddownunitdetail.StartupAndDownUnitDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 必开必停机组明细信息")
@RestController
@RequestMapping("/et/startup-and-down-unit-detail")
@Validated
public class StartupAndDownUnitDetailController {

    @Resource
    private StartupAndDownUnitDetailService startupAndDownUnitDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建必开必停机组明细信息")
    @PreAuthorize("@ss.hasPermission('jy:startup-and-down-unit-detail:create')")
    public CommonResult<Long> createStartupAndDownUnitDetail(@Valid @RequestBody StartupAndDownUnitDetailSaveReqVO createReqVO) {
        return success(startupAndDownUnitDetailService.createStartupAndDownUnitDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新必开必停机组明细信息")
    @PreAuthorize("@ss.hasPermission('jy:startup-and-down-unit-detail:update')")
    public CommonResult<Boolean> updateStartupAndDownUnitDetail(@Valid @RequestBody StartupAndDownUnitDetailSaveReqVO updateReqVO) {
        startupAndDownUnitDetailService.updateStartupAndDownUnitDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除必开必停机组明细信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('jy:startup-and-down-unit-detail:delete')")
    public CommonResult<Boolean> deleteStartupAndDownUnitDetail(@RequestParam("id") Long id) {
        startupAndDownUnitDetailService.deleteStartupAndDownUnitDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得必开必停机组明细信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('jy:startup-and-down-unit-detail:query')")
    public CommonResult<StartupAndDownUnitDetailRespVO> getStartupAndDownUnitDetail(@RequestParam("id") Long id) {
        StartupAndDownUnitDetailDO startupAndDownUnitDetail = startupAndDownUnitDetailService.getStartupAndDownUnitDetail(id);
        return success(BeanUtils.toBean(startupAndDownUnitDetail, StartupAndDownUnitDetailRespVO.class));
    }


}