package cn.iocoder.yudao.module.et.service.dataproc.outer;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.et.dal.dataobject.MarketMember.MarketMemberDO;
import cn.iocoder.yudao.module.et.dal.dataobject.contracts.ContractsDO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractsequipment.ContractsEquipmentDO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractsmember.ContractsMemberDO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractsmonth12.ContractsMonth12DO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractspowercurve.ContractsPowerCurveDO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractstime24.ContractsTime24DO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractstype.ContractsTypeDO;
import cn.iocoder.yudao.module.et.dal.dataobject.seriescontracts.SeriesContractsDO;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingUnitOrg.TradingUnitOrgDO;
import cn.iocoder.yudao.module.et.dal.mysql.MarketMember.MarketMemberMapper;
import cn.iocoder.yudao.module.et.dal.mysql.contracts.ContractsMapper;
import cn.iocoder.yudao.module.et.dal.mysql.contractsequipment.ContractsEquipmentMapper;
import cn.iocoder.yudao.module.et.dal.mysql.contractsmember.ContractsMemberMapper;
import cn.iocoder.yudao.module.et.dal.mysql.contractsmonth12.ContractsMonth12Mapper;
import cn.iocoder.yudao.module.et.dal.mysql.contractspowercurve.ContractsPowerCurveMapper;
import cn.iocoder.yudao.module.et.dal.mysql.contractstime24.ContractsTime24Mapper;
import cn.iocoder.yudao.module.et.dal.mysql.contractstype.ContractsTypeMapper;
import cn.iocoder.yudao.module.et.dal.mysql.seriescontracts.SeriesContractsMapper;
import cn.iocoder.yudao.module.et.dal.mysql.tradingUnitOrg.TradingUnitOrgMapper;
import cn.iocoder.yudao.module.et.service.dataproc.base.BaseDataProc;
import cn.jy.soft.remote.bean.SocketPacket;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * GS-A-55 我的合同-当前合同
 *
 * <AUTHOR>
 * @date 2025-03-16
 **/
@Slf4j
@Service
@AllArgsConstructor
public class CurrentContractProc implements BaseDataProc {
    @Resource
    public ContractsEquipmentMapper contractsEquipmentMapper;

    @Resource
    public ContractsMemberMapper contractsMemberMapper;

    @Resource
    public ContractsMonth12Mapper contractsMonth12Mapper;

    @Resource
    public ContractsTime24Mapper contractsTime24Mapper;

    @Resource
    public ContractsMapper contractsMapper;

    @Resource
    public ContractsPowerCurveMapper contractsPowerCurveMapper;

    @Resource
    public MongoTemplate mongoTemplate;

    @Resource
    public SeriesContractsMapper seriesContractsMapper;

    @Resource
    public ContractsTypeMapper contractsTypeMapper;

    @Resource
    public MarketMemberMapper marketMemberMapper;

    @Resource
    public TradingUnitOrgMapper tradingUnitOrgMapper;
//                List<ContractsPowerCurveDO> contractsPowerCurveDOList = new ArrayList<>();    // 电力曲线

    @Override
    public boolean execute(SocketPacket dto) {
        long runtime = System.currentTimeMillis();
        final String[] data = {""};
        try {
            TenantUtils.execute(dto.getTenantId(), () -> {
                data[0] = dto.getData();
                JSONObject jsonObject = JSONUtil.parseObj(dto.getData());
                ContractsDO contractsDO = new ContractsDO();    // 合同信息
                ContractsMemberDO contractsMemberDO = new ContractsMemberDO();    // 合同成员信息
                List<ContractsMonth12DO> contractsMonth12DOList = new ArrayList<>();    // 合同分月信息
                List<ContractsTime24DO> contractsTime24DOList = new ArrayList<>();   // 合同分时段信息
                List<ContractsEquipmentDO> contractsEquipmentDOList = new ArrayList<>();    // 合同机组信息
                SeriesContractsDO seriesContractsDONew = new SeriesContractsDO();    // 合同序列信息
                Long marketMemberSellId = null;//售电方主体id
                Long marketMemberPullId = null;//购电方主体id
                List<CsvRow> rowList;
                Long tenantId = dto.getTenantId();
                String orgCode = dto.getOrgCode().toUpperCase();
                TradingUnitOrgDO tradingUnitOrgDO = tradingUnitOrgMapper.selectOne(new QueryWrapper<TradingUnitOrgDO>().eq("code", orgCode));
                String deptId = tradingUnitOrgDO.getDeptId();
                String bizCode = dto.getBizCode();
                //交易系统主体id
                String membersId = null;
                //市场主体名称
                String saleParticipantname = null;
                //交易系统单元id
                String unitId = null;
                //交易系统单元名称
                String generatorName = null;
                //交易系统合同id
                String sysContractId = null;
                for (String bidDate : jsonObject.keySet()) {
                    //解析售电方市场主体信息
                    String accessoryBaseInfo = jsonObject.getJSONObject(bidDate).getStr("accessoryBaseInfo");
                    if (accessoryBaseInfo == null || "".equals(accessoryBaseInfo)) {
                        try {
                            JSONObject jsonObjectId = JSONUtil.parseObj(jsonObject.getJSONObject(bidDate).getStr("contractBaseInfo"));
                            JSONObject jsonObjectIdJ = (JSONObject) jsonObjectId.get("data");
                            String stringId = String.valueOf(jsonObjectIdJ.get("contractId"));
                            sysContractId = stringId;
                        } catch (Exception e) {
                            log.warn("合同id为空");
                            throw new RuntimeException("合同id为空");
                        }
                    } else {
                        JSONObject jsonObject1 = JSONUtil.parseObj(accessoryBaseInfo);
                        String message = Convert.toStr(jsonObject1.get("message"));
                        if (message.equals("Success")) {
                            JSONObject accessoryBaseInfoData = (JSONObject) jsonObject1.get("data");
                            JSONArray listArray = accessoryBaseInfoData.getJSONArray("list");
                            if (!listArray.isEmpty()) {
                                for (Object item : listArray) {
                                    JSONObject recordObj = (JSONObject) item;
                                    //交易系统主体id
                                    membersId = recordObj.getStr("membersId");
                                    //市场主体名称
                                    saleParticipantname = recordObj.getStr("saleParticipantname");
                                    //交易系统单元id
                                    unitId = recordObj.getStr("unitId");
                                    String contractId = recordObj.getStr("contractId");

                                    sysContractId = contractId;
                                    String role = recordObj.getStr("role");
                                    if ("购电方".equals(role))
                                        generatorName = recordObj.getStr("vendeeUnitName");
                                    else
                                        generatorName = recordObj.getStr("saleUnitName");
                                    //市场主体信息
                                    MarketMemberDO marketMemberDO = marketMemberMapper.selectByName(saleParticipantname, generatorName);
                                    if (marketMemberDO == null) {
                                        TradingUnitOrgDO sellerTradingUnitDO = tradingUnitOrgMapper.selectByTradingUnitOfficialName(generatorName);
                                        MarketMemberDO ins = MarketMemberDO.builder()
                                                .sysMembersId(membersId)
                                                .sysMembersName(saleParticipantname)
                                                .sysUnitId(unitId)
                                                .sysUnitName(generatorName)
                                                .dataSource(1)
                                                .tenantId(String.valueOf(tenantId))
                                                .name(saleParticipantname)
                                                .tradingUnitId(sellerTradingUnitDO == null ? null : String.valueOf(sellerTradingUnitDO.getId()))
                                                .build();
                                        marketMemberMapper.insert(ins);
                                        if ("购电方".equals(role))
                                            marketMemberPullId = ins.getId();
                                        else
                                            marketMemberSellId = ins.getId();
                                    } else {
                                        if ("购电方".equals(role))
                                            marketMemberPullId = marketMemberDO.getId();
                                        else
                                            marketMemberSellId = marketMemberDO.getId();
                                    }
                                }

                            } else {
                                if (sysContractId == null) {
                                    try {
                                        JSONObject jsonObjectId = JSONUtil.parseObj(jsonObject.getJSONObject(bidDate).getStr("contractBaseInfo"));
                                        JSONObject jsonObjectIdJ = (JSONObject) jsonObjectId.get("data");
                                        String stringId = String.valueOf(jsonObjectIdJ.get("contractId"));
                                        sysContractId = stringId;
                                    } catch (Exception e) {
                                        log.warn("合同id为空");
                                        throw new RuntimeException("合同id为空");
                                    }
                                }
                            }
                        }
                    }

                    for (String tradingSeq : jsonObject.getJSONObject(bidDate).keySet()) {
                        rowList = CsvUtil.getReader(new CsvReadConfig().setBeginLineNo(0).setTrimField(true))
                                .readFromStr(jsonObject.getJSONObject(bidDate).getStr(tradingSeq)).getRows();
                        if (!rowList.isEmpty()) {
                            if ("basic".equals(tradingSeq)) {
                                //合同类型
                                String contractsType = contractsDO.getContractsType();
                                //交易序列
                                String seriesContracts = contractsDO.getSeriesContracts();
                                contractsDO.setValueByRow(rowList);
                                contractsDO.setTenantId(tenantId);
                                contractsDO.setOrgCode(orgCode);
                                contractsDO.setDeleted(false);
                                if (bizCode.contains("55")) {
                                    contractsDO.setHistoryContract(false);
                                }
                                if (bizCode.contains("56")) {
                                    contractsDO.setHistoryContract(true);
                                }
                                ContractsTypeDO contractsTypeDO = contractsTypeMapper.selectByName(contractsType);
                                if (contractsTypeDO == null) {
                                    log.warn("合同类型:" + contractsDO.getContractsType() + " 不存在");
                                    continue;
                                }
                                contractsDO.setContractsTypeId(Convert.toInt(contractsTypeDO.getId()));
                                //查询合同序列
                                seriesContractsDONew = seriesContractsMapper.selectByName(seriesContracts);
                                if (seriesContractsDONew == null) {
                                    seriesContractsDONew = new SeriesContractsDO();
                                    seriesContractsDONew.setName(contractsDO.getSeriesContracts());
                                    seriesContractsDONew.setContractsTypeId(contractsTypeDO.getId());
                                    seriesContractsDONew.setDeleted(false);
                                    seriesContractsDONew.setTenantId(tenantId);
                                    seriesContractsDONew.setOrgCode(orgCode);
                                    //todo 交易系统序列id
//                                    seriesContractsDONew.setSysSequenceId("1");
                                    seriesContractsMapper.insert(seriesContractsDONew);
                                }
                                contractsDO.setSysContractId(sysContractId);
                                contractsDO.setDeptId(Long.valueOf(deptId));
                                contractsDO.setSeriesContractsId(seriesContractsDONew.getId());
                                //合同基本信息
                                ContractsDO contractsDO1 = contractsMapper.selectByContractId(contractsDO.getSysContractId());
                                if (contractsDO1 != null) {
                                    contractsDO.setId(contractsDO1.getId());
                                    contractsMapper.updateById(contractsDO);
                                } else {
                                    contractsMapper.insert(contractsDO);
                                }
                                // 判断合同类型-省间合同, 同一交易单元, 同一交易序列.  只有一条数据  删除之前的数据
                                if (contractsType.equals("省间合同交易合同1")) {
                                    List<ContractsDO> contractsDeleteList = contractsMapper.selectList(new QueryWrapper<ContractsDO>()
                                            .eq("series_contracts_id", seriesContractsDONew.getId())
                                            .eq("org_code", orgCode)
                                    );
                                    for (ContractsDO aDo : contractsDeleteList) {
                                        // 修复：正确比较两个对象的ID，避免删除当前正在处理的合同
                                        // 添加空值检查，提高代码健壮性
                                        if (aDo.getId() != null && contractsDO.getId() != null &&
                                            !aDo.getId().equals(contractsDO.getId())) {
                                            Long deleteId = aDo.getId();
                                            log.info("删除省间合同交易合同1的旧数据，合同ID: {}, 合同名称: {}", deleteId, aDo.getName());
                                            contractsMapper.deleteById(deleteId);
                                            contractsMemberMapper.deleteByContractNo(deleteId);
                                            contractsMonth12Mapper.deleteByContractNo(deleteId);
                                            contractsTime24Mapper.deleteByContractNo(deleteId);
                                            contractsEquipmentMapper.deleteByContractNo(deleteId);
                                            contractsPowerCurveMapper.deleteByContractNo(deleteId);
                                            mongoTemplate.remove(Query.query(Criteria.where("contractsId").is(deleteId)), "constructs_zjn0101");
                                        }
                                    }
                                }
                            }
                            // 合同方信息
                            if ("party".equals(tradingSeq)) {
                                contractsMemberDO.setValueByRow(rowList);
                                contractsMemberDO.setContractsId(contractsDO.getId());
                                contractsMemberDO.setTenantId(tenantId);
                                contractsMemberDO.setOrgCode(orgCode);
                                contractsMemberDO.setDeptId(Long.valueOf(deptId));
                                contractsMemberDO.setSellerTradingUnitId(marketMemberSellId);
                                contractsMemberDO.setPurchaserTradingUnitId(marketMemberPullId);
                                contractsMemberMapper.deleteByContractNo(contractsMemberDO.getContractsId());
                                contractsMemberMapper.insert(contractsMemberDO);
                            }
                        }
                        // 合同分月信息
                        if ("monthly".equals(tradingSeq)) {
                            String str = jsonObject.getJSONObject(bidDate).getStr(tradingSeq);
                            String[] lines = str.split("\n");
                            String[] title = lines[0].split("\\$\\|\\$");
                            if (lines.length > 1) {
                                for (int i = 1; i < lines.length; i++) {
                                    String line = lines[i].trim();
                                    if (line.isEmpty()) {
                                        continue;
                                    }
                                    String[] fields = line.split("\\$\\|\\$");
                                    ContractsMonth12DO contractsMonth12DO = new ContractsMonth12DO();
                                    contractsMonth12DO.setContractsId(contractsDO.getId());
                                    contractsMonth12DO.setTenantId(tenantId);
                                    contractsMonth12DO.setOrgCode(orgCode);
                                    int startDateIndex = findIndex(title, "开始日期");
                                    int endDateIndex = findIndex(title, "结束日期");
                                    int quantityIndex = findIndex(title, "分月电量");
                                    int price = findIndex(title, "分月电价");
                                    if (startDateIndex >= 0 && startDateIndex < fields.length)
                                        contractsMonth12DO.setBeginTime(StrUtil.isNotBlank(fields[startDateIndex].trim()) ? DateUtil.toLocalDateTime(DateUtil.parse(fields[startDateIndex].trim(), "yyyy-MM-dd")) : null);
                                    if (endDateIndex >= 0 && endDateIndex < fields.length)
                                        contractsMonth12DO.setEndTime(StrUtil.isNotBlank(fields[endDateIndex].trim()) ? DateUtil.toLocalDateTime(DateUtil.parse(fields[endDateIndex].trim(), "yyyy-MM-dd")) : null);
                                    if (quantityIndex >= 0 && quantityIndex < fields.length)
                                        contractsMonth12DO.setQuantity(new BigDecimal(fields[quantityIndex]));
                                    if (price >= 0 && price < fields.length) {
                                        contractsMonth12DO.setPrice(new BigDecimal(fields[price]));
                                    }
                                    contractsMonth12DO.setDeptId(Long.valueOf(deptId));
                                    contractsMonth12DO.setDeleted(false);
                                    contractsMonth12DOList.add(contractsMonth12DO);
                                }
                            }
                            if (!contractsMonth12DOList.isEmpty()) {
                                contractsMonth12Mapper.deleteByContractNo(contractsDO.getId());
                                contractsMonth12Mapper.insertBatch(contractsMonth12DOList);
                            }
                        }
                        // 合同分时段信息
                        if ("timeSlot".equals(tradingSeq)) {
                            String str = jsonObject.getJSONObject(bidDate).getStr(tradingSeq);
                            String[] lines = str.split("\n");
                            String[] title = lines[0].split("\\$\\|\\$");
                            if (lines.length > 1) {
                                for (int i = 1; i < lines.length; i++) {
                                    String line = lines[i].trim();
                                    if (line.isEmpty()) {
                                        continue;
                                    }
                                    String[] fields = line.split("\\$\\|\\$");
                                    ContractsTime24DO contractsTime24DO = new ContractsTime24DO();
                                    contractsTime24DO.setContractsId(contractsDO.getId());
                                    contractsTime24DO.setTenantId(tenantId);
                                    contractsTime24DO.setOrgCode(orgCode);
                                    int timeSlotCodingIndex = findIndex(title, "时段编号");
                                    int timeSlotNameIndex = findIndex(title, "时段名称");
                                    int timeSlotsRangeIndex = findIndex(title, "时段区间");
                                    int purchaserQuantityIndex = findIndex(title, "购方电量");
                                    int sellerQuantityIndex = findIndex(title, "售方电量");
                                    int purchaserPriceIndex = findIndex(title, "购方电价");
                                    int sellerPriceIndex = findIndex(title, "售方电价");
                                    int timeSlotsIndex = findIndex(title, "时间段");
                                    int beginTimeIndex = findIndex(title, "时间段开始日期");
                                    int endTimeIndex = findIndex(title, "时间段结束日期");
                                    if (timeSlotCodingIndex >= 0 && timeSlotCodingIndex < fields.length)
                                        contractsTime24DO.setTimeSlotCoding(fields[timeSlotCodingIndex]);
                                    if (timeSlotNameIndex >= 0 && timeSlotNameIndex < fields.length)
                                        contractsTime24DO.setTimeSlotName(fields[timeSlotNameIndex]);
                                    if (timeSlotsRangeIndex >= 0 && timeSlotsRangeIndex < fields.length)
                                        contractsTime24DO.setTimeSlotsRange(fields[timeSlotsRangeIndex]);
                                    if (purchaserQuantityIndex >= 0 && purchaserQuantityIndex < fields.length)
                                        contractsTime24DO.setPurchaserQuantity(new BigDecimal(fields[purchaserQuantityIndex]));
                                    if (sellerQuantityIndex >= 0 && sellerQuantityIndex < fields.length)
                                        contractsTime24DO.setSellerQuantity(new BigDecimal(fields[sellerQuantityIndex]));
                                    if (purchaserPriceIndex >= 0 && purchaserPriceIndex < fields.length)
                                        contractsTime24DO.setPurchaserPrice(new BigDecimal(fields[purchaserPriceIndex]));
                                    if (sellerPriceIndex >= 0 && sellerPriceIndex < fields.length)
                                        contractsTime24DO.setSellerPrice(new BigDecimal(fields[sellerPriceIndex]));
                                    if (timeSlotsIndex >= 0 && timeSlotsIndex < fields.length)
                                        contractsTime24DO.setTimeSlots(fields[timeSlotsIndex]);
                                    if (beginTimeIndex >= 0 && beginTimeIndex < fields.length)
                                        contractsTime24DO.setBeginTime(StrUtil.isNotBlank(fields[beginTimeIndex].trim()) ? DateUtil.toLocalDateTime(DateUtil.parse(fields[beginTimeIndex].trim(), "yyyy-MM-dd")) : null);
                                    if (endTimeIndex >= 0 && endTimeIndex < fields.length)
                                        contractsTime24DO.setEndTime(StrUtil.isNotBlank(fields[endTimeIndex].trim()) ? DateUtil.toLocalDateTime(DateUtil.parse(fields[endTimeIndex].trim(), "yyyy-MM-dd")) : null);
                                    contractsTime24DO.setDeleted(false);
                                    contractsTime24DO.setDeptId(Long.valueOf(deptId));
                                    contractsTime24DOList.add(contractsTime24DO);
                                }
                            }
                            if (!contractsTime24DOList.isEmpty()) {
                                contractsTime24Mapper.deleteByContractNo(contractsDO.getId());
                                contractsTime24Mapper.insertBatch(contractsTime24DOList);
                            }
                        }
                        // 合同机组信息
                        if ("generatorUnit".equals(tradingSeq)) {
                            String str = jsonObject.getJSONObject(bidDate).getStr(tradingSeq);
                            // 按行分割数据
                            String[] lines = str.split("\n");
                            String[] title = lines[0].split("\\$\\|\\$");
                            if (lines.length > 1) { // 确保至少有表头和一行数据
                                // 第一行是表头，从第二行开始处理数据
                                for (int i = 1; i < lines.length; i++) {
                                    String line = lines[i].trim();
                                    if (line.isEmpty()) {
                                        continue;
                                    }
                                    // 按$|$分割字段
                                    String[] fields = line.split("\\$\\|\\$");
                                    ContractsEquipmentDO contractsEquipmentDO = new ContractsEquipmentDO();

                                    // 根据title查找对应的字段位置
                                    int contractRoleIndex = findIndex(title, "合同角色");
                                    int equipmentNameIndex = findIndex(title, "机组名称");
                                    int equipmentTypeIndex = findIndex(title, "机组类型");
                                    int installedCapacityIndex = findIndex(title, "装机容量");
                                    int approvedPriceIndex = findIndex(title, "批复电价");
                                    int startDateIndex = findIndex(title, "开始日期");
                                    int endDateIndex = findIndex(title, "结束日期");
                                    int equipmentPowerIndex = findIndex(title, "机组电量");
                                    int timePeriodNameIndex = findIndex(title, "时段名称");
                                    int equityElectricityPriceIndex = findIndex(title, "权益电价");
                                    int timePeriodIndex = findIndex(title, "时段起止时间");
                                    // 设置字段值，确保索引有效
                                    if (contractRoleIndex >= 0 && contractRoleIndex < fields.length) {
                                        contractsEquipmentDO.setContractsRoleName(fields[contractRoleIndex]);
                                    }

                                    if (equipmentNameIndex >= 0 && equipmentNameIndex < fields.length) {
                                        contractsEquipmentDO.setEquipmentName(fields[equipmentNameIndex]);
                                    }
                                    if (equipmentTypeIndex >= 0 && equipmentTypeIndex < fields.length) {
                                        contractsEquipmentDO.setEquipmentType(fields[equipmentTypeIndex]);
                                    }
                                    if (equityElectricityPriceIndex >= 0 && equityElectricityPriceIndex < fields.length) {
                                        contractsEquipmentDO.setEquityElectricityPrice(new BigDecimal(fields[equityElectricityPriceIndex]));
                                    }
                                    if (installedCapacityIndex >= 0 && installedCapacityIndex < fields.length) {
                                        try {
                                            contractsEquipmentDO.setQuantity(new BigDecimal(fields[installedCapacityIndex]));
                                        } catch (Exception e) {
                                            contractsEquipmentDO.setQuantity(BigDecimal.ZERO);
                                        }
                                    }
                                    if (approvedPriceIndex >= 0 && approvedPriceIndex < fields.length) {
                                        try {
                                            contractsEquipmentDO.setApprovedPrice(new BigDecimal(fields[approvedPriceIndex]));
                                        } catch (Exception e) {
                                            contractsEquipmentDO.setApprovedPrice(BigDecimal.ZERO);
                                        }
                                    }
                                    if (startDateIndex >= 0 && startDateIndex < fields.length) {
                                        try {
                                            contractsEquipmentDO.setBeginTime(DateUtil.toLocalDateTime(DateUtil.parse(fields[startDateIndex], "yyyy-MM-dd")));
                                        } catch (Exception e) {
                                        }
                                    }
                                    if (endDateIndex >= 0 && endDateIndex < fields.length) {
                                        try {
                                            contractsEquipmentDO.setEndTime(DateUtil.toLocalDateTime(DateUtil.parse(fields[endDateIndex], "yyyy-MM-dd")));
                                        } catch (Exception e) {
                                        }
                                    }
                                    if (equipmentPowerIndex >= 0 && equipmentPowerIndex < fields.length) {
                                        try {
                                            contractsEquipmentDO.setEquipmentGen(new BigDecimal(fields[equipmentPowerIndex]));
                                        } catch (Exception e) {
                                            contractsEquipmentDO.setEquipmentGen(BigDecimal.ZERO);
                                        }
                                    }
                                    if (timePeriodNameIndex >= 0 && timePeriodNameIndex < fields.length) {
                                        contractsEquipmentDO.setTimeSlotName(fields[timePeriodNameIndex]);
                                    }
                                    if (timePeriodIndex >= 0 && timePeriodIndex < fields.length) {
                                        contractsEquipmentDO.setTimeSlotRange(fields[timePeriodIndex]);
                                    }
                                    contractsEquipmentDO.setContractsId(contractsDO.getId());
                                    contractsEquipmentDO.setTenantId(tenantId);
                                    contractsEquipmentDO.setDeleted(false);
                                    contractsEquipmentDO.setOrgCode(orgCode);
                                    contractsEquipmentDO.setDeptId(Long.valueOf(deptId));
                                    contractsEquipmentDOList.add(contractsEquipmentDO);
                                }
                            }

                            if (!contractsEquipmentDOList.isEmpty()) {
                                contractsEquipmentMapper.deleteByContractNo(contractsDO.getId());
                                contractsEquipmentMapper.insertBatch(contractsEquipmentDOList);
                            }
                        }
                    }
                }
            });
            return true;
        } catch (Exception ex) {
            log.error(StrUtil.format("[{}] > 解析出现异常", dto.getBizCode()), ex);
            log.error(StrUtil.format("[{}] > 解析出现异常源数据: ", data[0]), ex);
            return false;
        }
    }
    /**
     * 查找字段在标题数组中的索引
     *
     * @param titles    标题数组
     * @param fieldName 字段名
     * @return 索引，如果未找到返回-1
     */
    private int findIndex(String[] titles, String fieldName) {
        if (titles == null || fieldName == null) {
            return -1;
        }
        for (int i = 0; i < titles.length; i++) {
            if (fieldName.equals(titles[i])) {
                return i;
            }
        }
        return -1;
    }
}
