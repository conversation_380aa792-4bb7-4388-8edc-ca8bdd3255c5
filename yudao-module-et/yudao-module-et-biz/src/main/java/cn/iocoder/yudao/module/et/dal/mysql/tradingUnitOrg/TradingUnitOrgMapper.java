package cn.iocoder.yudao.module.et.dal.mysql.tradingUnitOrg;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingUnitOrg.TradingUnitOrgDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 交易单元组织机构 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TradingUnitOrgMapper extends BaseMapperX<TradingUnitOrgDO> {
    @TenantIgnore
    @Select("SELECT * from jy_trading_unit where name = #{name}")
    TradingUnitOrgDO selectByTradingUnitName(@Param("name") String name);

    @TenantIgnore
    @Select("SELECT * from jy_trading_unit where official_name = #{officialName}")
    TradingUnitOrgDO selectByTradingUnitOfficialName(@Param("officialName") String officialName);

    @TenantIgnore
    @Select("SELECT * from jy_trading_unit where code = #{code} and tenant_id = #{tenantId}")
    TradingUnitOrgDO selectByTradingUnitCode(@Param("code") String code, @Param("tenantId") Long tenantId);
}
