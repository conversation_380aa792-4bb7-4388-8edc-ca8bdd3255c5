package cn.iocoder.yudao.module.et.controller.admin.similardayprediction.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 相似日表 新增/修改 Request VO")
@Data
public class SimilarDayPredictionSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "6764")
    private Long id;

    @Schema(description = "预测日", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long forecastDay;

    @Schema(description = "相似日", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime similarDay;

    @Schema(description = "相似度")
    private Double similarity;

    @Schema(description = "相似度排序", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer sort;

    @Schema(description = "部门ID", example = "20464")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "修改时间")
    private LocalDateTime modificationTime;

}