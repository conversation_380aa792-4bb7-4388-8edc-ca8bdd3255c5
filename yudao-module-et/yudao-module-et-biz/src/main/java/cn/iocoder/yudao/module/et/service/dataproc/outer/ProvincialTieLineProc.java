package cn.iocoder.yudao.module.et.service.dataproc.outer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.et.dal.dataobject.companyunit.CompanyUnitDO;
import cn.iocoder.yudao.module.et.dal.tdengine.dataproc.PublicDataProcMapper;
import cn.iocoder.yudao.module.et.service.dataproc.base.BaseDataProc;
import cn.iocoder.yudao.module.et.util.JyDateTimeUtil;
import cn.jy.soft.remote.bean.SocketPacket;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * GS-A-03 供需与约束-预测信息-省间联络线输电曲线预测
 *
 * <AUTHOR>
 * @date 2024-11-12
 **/
@Slf4j
@Service
@AllArgsConstructor
public class ProvincialTieLineProc implements BaseDataProc {

    private final PublicDataProcMapper publicDataProcMapper;

    @Override
    public boolean execute(SocketPacket dto) {
        long runtime = System.currentTimeMillis();
        try {
            TenantUtils.execute(dto.getTenantId(), () -> {
                JSONObject jsonObject = JSONUtil.parseObj(dto.getData());
                List<CompanyUnitDO> companyUnitDOList = CollUtil.newArrayList();
                List<CsvRow> dayaheadRowList;
                List<CsvRow> intradayRowList;
                CompanyUnitDO entity;
                List<Date> timePoints;
                JSONObject subJsonObject;
                for (String key : jsonObject.keySet()) {
                    subJsonObject = jsonObject.getJSONObject(key);
                    if (ObjectUtil.isNotEmpty(subJsonObject)) {
                        boolean b1 = ObjectUtil.isNotNull(subJsonObject.getStr("dayahead"));
                        boolean b2 = ObjectUtil.isNotNull(subJsonObject.getStr("intraday"));
                        timePoints = JyDateTimeUtil.get96TimePoints(DateUtil.parseDate(key), 1);
                        dayaheadRowList =
                            b1 ? CsvUtil.getReader(new CsvReadConfig().setBeginLineNo(0).setTrimField(true))
                                .readFromStr(subJsonObject.getStr("dayahead")).getRows() : ListUtil.empty();
                        intradayRowList =
                            b2 ? CsvUtil.getReader(new CsvReadConfig().setBeginLineNo(0).setTrimField(true))
                                .readFromStr(subJsonObject.getStr("intraday")).getRows() : ListUtil.empty();
                        for (int i = 0; i < timePoints.size(); i++) {
                            entity = new CompanyUnitDO();
                            // 时间
                            entity.setTs(Timestamp.valueOf(DateUtil.formatDateTime(timePoints.get(i))));
                            if (b1) {
                                // 日前
                                for (CsvRow dayaheadRow : dayaheadRowList) {
                                    switch (dayaheadRow.get(0)) {
                                        case "甘肃_新疆":
                                            entity.setHisLlxGsXj(Float.parseFloat(dayaheadRow.get(i + 1)));
                                            break;
                                        case "甘肃_青海":
                                            entity.setHisLlxGsQh(Float.parseFloat(dayaheadRow.get(i + 1)));
                                            break;
                                        case "甘肃_宁夏":
                                            entity.setHisLlxGsNx(Float.parseFloat(dayaheadRow.get(i + 1)));
                                            break;
                                        case "甘肃_陕西":
                                            entity.setHisLlxGsSx(Float.parseFloat(dayaheadRow.get(i + 1)));
                                            break;
                                        case "甘肃_湖南":
                                            entity.setHisLlxGsHn(Float.parseFloat(dayaheadRow.get(i + 1)));
                                            break;
                                        case "甘肃_山东":
                                            entity.setHisLlxGsSd(Float.parseFloat(dayaheadRow.get(i + 1)));
                                            break;
                                        default:
                                            log.error("日前联络线[{}]不存在！", dayaheadRow.get(0));
                                            break;
                                    }
                                }
                            }
                            if (b2) {
                                // 日内
                                for (CsvRow intradayRow : intradayRowList) {
                                    switch (intradayRow.get(0)) {
                                        case "甘肃_新疆":
                                            entity.setRealLlxGsXj(Float.parseFloat(intradayRow.get(i + 1)));
                                            break;
                                        case "甘肃_青海":
                                            entity.setRealLlxGsQh(Float.parseFloat(intradayRow.get(i + 1)));
                                            break;
                                        case "甘肃_宁夏":
                                            entity.setRealLlxGsNx(Float.parseFloat(intradayRow.get(i + 1)));
                                            break;
                                        case "甘肃_陕西":
                                            entity.setRealLlxGsSx(Float.parseFloat(intradayRow.get(i + 1)));
                                            break;
                                        case "甘肃_湖南":
                                            entity.setRealLlxGsHn(Float.parseFloat(intradayRow.get(i + 1)));
                                            break;
                                        case "甘肃_山东":
                                            entity.setRealLlxGsSd(Float.parseFloat(intradayRow.get(i + 1)));
                                            break;
                                        default:
                                            log.error("日内联络线[{}]不存在！", intradayRow.get(0));
                                            break;
                                    }
                                }
                            }
                            companyUnitDOList.add(entity);
                        }
                    }
                }
                if (CollUtil.isNotEmpty(companyUnitDOList)) {
                    publicDataProcMapper.insertProvincialTieLine(dto.getOrgCode(), companyUnitDOList);
                    log.info("[{}] > 解析入库完成，共 {} 条，用时 {} ms", dto.getBizCode(), companyUnitDOList.size(),
                        System.currentTimeMillis() - runtime);
                }
            });
        } catch (Exception ex) {
            log.error(StrUtil.format("[{}] > 解析出现异常", dto.getBizCode()), ex);
            return false;
        }
        return true;
    }
}
