package cn.iocoder.yudao.module.et.controller.admin.powerstation;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.et.controller.admin.powerstation.vo.PowerStationPageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.powerstation.vo.PowerStationRespVO;
import cn.iocoder.yudao.module.et.controller.admin.powerstation.vo.PowerStationSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.powerstation.PowerStationDO;
import cn.iocoder.yudao.module.et.service.powerstation.PowerStationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 场站信息")
@RestController
@RequestMapping("/et/power-station")
@Validated
public class PowerStationController {


    @Resource
    private PowerStationService powerStationService;

    @GetMapping("/getTradingUnit")
    @Operation(summary = "获取当前区域公司下的所有交易单元")
    //@PreAuthorize("@ss.hasPermission('et:power-station:create')")
    public CommonResult<List<Map<String, Object>>> getTradingUnit() {
        return success(powerStationService.getTradingUnit());
    }

    @GetMapping("/getGroupTree")
    @Operation(summary = "获取当前用户下的所有部门及交易单元")
    //@PreAuthorize("@ss.hasPermission('et:power-station:create')")
    public CommonResult<List<Map<String, Object>>> getGroupTree() {
        return success(powerStationService.getGroup());
    }

    @PostMapping("/create")
    @Operation(summary = "创建场站信息")
    @PreAuthorize("@ss.hasPermission('jy:power-station:create')")
    public CommonResult<Integer> createPowerStation(@Valid @RequestBody PowerStationSaveReqVO createReqVO) {
        return success(powerStationService.createPowerStation(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新场站信息")
    @PreAuthorize("@ss.hasPermission('jy:power-station:update')")
    public CommonResult<Boolean> updatePowerStation(@Valid @RequestBody PowerStationSaveReqVO updateReqVO) {
        powerStationService.updatePowerStation(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除场站信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('jy:power-station:delete')")
    public CommonResult<Boolean> deletePowerStation(@RequestParam("id") Integer id) {
        powerStationService.deletePowerStation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得场站信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('jy:power-station:query')")
    public CommonResult<PowerStationRespVO> getPowerStation(@RequestParam("id") Integer id) {
        PowerStationDO powerStation = powerStationService.getPowerStation(id);
        return success(BeanUtils.toBean(powerStation, PowerStationRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得场站信息分页")
    @PreAuthorize("@ss.hasPermission('jy:power-station:query')")
    public CommonResult<PageResult<PowerStationRespVO>> getPowerStationPage(@Valid PowerStationPageReqVO pageReqVO) {
        PageResult<PowerStationDO> pageResult = powerStationService.getPowerStationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PowerStationRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出场站信息 Excel")
    @PreAuthorize("@ss.hasPermission('jy:power-station:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPowerStationExcel(@Valid PowerStationPageReqVO pageReqVO,
                                        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PowerStationDO> list = powerStationService.getPowerStationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "场站信息.xls", "数据", PowerStationRespVO.class,
                BeanUtils.toBean(list, PowerStationRespVO.class));
    }

}