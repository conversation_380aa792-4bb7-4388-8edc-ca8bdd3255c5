package cn.iocoder.yudao.module.et.controller.admin.tradingmarket;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.et.controller.admin.tradingmarket.vo.*;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingmarket.TradingMarketDO;
import cn.iocoder.yudao.module.et.service.tradingmarket.TradingMarketService;

@Tag(name = "管理后台 - 交易")
@RestController
@RequestMapping("/et/trading-market")
@Validated
public class TradingMarketController {

    @Resource
    private TradingMarketService tradingMarketService;

    @PostMapping("/create")
    @Operation(summary = "创建交易")
    @PreAuthorize("@ss.hasPermission('et:trading-market:create')")
    public CommonResult<Long> createTradingMarket(@Valid @RequestBody TradingMarketSaveReqVO createReqVO) {
        return success(tradingMarketService.createTradingMarket(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新交易")
    @PreAuthorize("@ss.hasPermission('et:trading-market:update')")
    public CommonResult<Boolean> updateTradingMarket(@Valid @RequestBody TradingMarketSaveReqVO updateReqVO) {
        tradingMarketService.updateTradingMarket(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除交易")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('et:trading-market:delete')")
    public CommonResult<Boolean> deleteTradingMarket(@RequestParam("id") Long id) {
        tradingMarketService.deleteTradingMarket(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得交易")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('et:trading-market:query')")
    public CommonResult<TradingMarketRespVO> getTradingMarket(@RequestParam("id") Long id) {
        TradingMarketDO tradingMarket = tradingMarketService.getTradingMarket(id);
        return success(BeanUtils.toBean(tradingMarket, TradingMarketRespVO.class));
    }


   @GetMapping("/getCode")
    @Operation(summary = "获得交易")
   public CommonResult<List<TradingMarketDO>> getAllCodeAndName() {

       List<TradingMarketDO> allCodeAndName = tradingMarketService.getAll();
       return success(BeanUtils.toBean(allCodeAndName, TradingMarketDO.class));
   }

    @GetMapping("/page")
    @Operation(summary = "获得交易分页")
    @PreAuthorize("@ss.hasPermission('et:trading-market:query')")
    public CommonResult<PageResult<TradingMarketRespVO>> getTradingMarketPage(@Valid TradingMarketPageReqVO pageReqVO) {
        PageResult<TradingMarketDO> pageResult = tradingMarketService.getTradingMarketPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TradingMarketRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出交易 Excel")
    @PreAuthorize("@ss.hasPermission('et:trading-market:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTradingMarketExcel(@Valid TradingMarketPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TradingMarketDO> list = tradingMarketService.getTradingMarketPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "交易.xls", "数据", TradingMarketRespVO.class,
                        BeanUtils.toBean(list, TradingMarketRespVO.class));
    }

}