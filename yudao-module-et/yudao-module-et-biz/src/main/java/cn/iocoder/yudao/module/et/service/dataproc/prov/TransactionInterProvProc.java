package cn.iocoder.yudao.module.et.service.dataproc.prov;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.et.dal.dataobject.transactionResults.TransactionInterProvDO;
import cn.iocoder.yudao.module.et.dal.mysql.transactionResults.TransactionInterProvMapper;
import cn.iocoder.yudao.module.et.enums.constants.Global;
import cn.iocoder.yudao.module.et.service.dataproc.base.BaseDataProc;
import cn.jy.soft.remote.bean.SocketPacket;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * BJ-A-02 省间中长期-交易结果
 *
 * <AUTHOR>
 * @date 2025-02-06
 **/
@Slf4j
@Service
@AllArgsConstructor
public class TransactionInterProvProc implements BaseDataProc {

    private final TransactionInterProvMapper transactionInterProvMapper;

    @Override
    public boolean execute(SocketPacket dto) {
        long runtime = System.currentTimeMillis();
        try {
            TenantUtils.execute(dto.getTenantId(), () -> {
                JSONObject pJsonObject = JSONUtil.parseObj(dto.getData());
                List<CsvRow> rowList;
                List<TransactionInterProvDO> transactionInterProvDOList = CollUtil.newArrayList();
                TransactionInterProvDO transactionInterProvDO;
                for (String d : pJsonObject.keySet()) {
                    JSONObject jsonObject = pJsonObject.getJSONObject(d);
                    for (String n : jsonObject.keySet()) {
                        rowList = CsvUtil.getReader(new CsvReadConfig().setBeginLineNo(0).setTrimField(true))
                            .readFromStr(jsonObject.getStr(n)).getRows();
                        if (!rowList.isEmpty()) {
                            String timeSlotCoding;
                            for (CsvRow row : rowList) {
                                timeSlotCoding = StrUtil.isNotBlank(row.get(3)) ? row.get(3) : Global.NOT_APPLICABLE;
                                transactionInterProvDO =
                                    transactionInterProvMapper.selectOneTransactionInterProv(n, row.get(1), row.get(2), timeSlotCoding,
                                        dto.getTenantId(), dto.getOrgCode());
                                if (ObjectUtil.isNotNull(transactionInterProvDO)) {
                                    transactionInterProvDO.setQuantity(new BigDecimal(row.get(4)))
                                        .setPrice(new BigDecimal(row.get(5)))
                                        .setGatherTime(DateUtil.toLocalDateTime(dto.getGatherTime()));
                                    transactionInterProvMapper.updateTransactionInterProv(transactionInterProvDO);
                                } else {
                                    transactionInterProvDOList.add(new TransactionInterProvDO()
                                        .setTransactionName(n)
                                        .setSellerName(row.get(1))
                                        .setTimePeriod(row.get(2))
                                        .setTimeSlotCoding(timeSlotCoding)
                                        .setQuantity(new BigDecimal(row.get(4)))
                                        .setPrice(new BigDecimal(row.get(5)))
                                        .setSellerPowerGrid(row.get(6))
                                        .setSellerTradingUnitName(row.get(7))
                                        .setTenantId(dto.getTenantId())
                                        .setOrgCode(StrUtil.sub(dto.getOrgCode(), 0, dto.getOrgCode().length() - 2))
                                        .setDispatchNodeCode(dto.getOrgCode())
                                        .setGatherTime(DateUtil.toLocalDateTime(dto.getGatherTime()))
                                    );
                                }
                            }
                        }
                    }
                }
                if (!transactionInterProvDOList.isEmpty()) {
                    transactionInterProvMapper.insertTransactionInterProvBatch(transactionInterProvDOList);
                }
                log.info("[{}] > 解析入库完成，用时 {} ms", dto.getBizCode(),
                    System.currentTimeMillis() - runtime);
            });
        } catch (Exception ex) {
            log.error(StrUtil.format("[{}] > 解析出现异常", dto.getBizCode()), ex);
            return false;
        }
        return true;
    }
}
