package cn.iocoder.yudao.module.et.dal.mysql.transactionResults;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.et.dal.dataobject.transactionResults.DailyRollingResultDetailDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

/**
 * 日滚动交易明细 Mapper
 *
 * <AUTHOR>
 */
@DS("shardingsphereDB")
@Mapper
public interface DailyRollingResultDetailMapper extends BaseMapperX<DailyRollingResultDetailDO> {
}
