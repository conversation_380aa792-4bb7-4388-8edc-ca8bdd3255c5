package cn.iocoder.yudao.module.et.dal.dataobject.transactionResults;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 日滚动交易明细 DO
 *
 * <AUTHOR>
 */
@TableName("jy_daily_rolling_result_detail")
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class DailyRollingResultDetailDO {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 父ID
     */
    private Long pid;
    /**
     * 买卖方向
     */
    private String tradingDirection;
    /**
     * 成交电量（MWh）
     */
    private BigDecimal quantity;
    /**
     * 成交电价（元/MWh）
     */
    private BigDecimal price;
    /**
     * 申报时间
     */
    private LocalDateTime submissionTime;
    /**
     * 成交时间
     */
    private LocalDateTime tradeTime;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 交易单元编码
     */
    @TableField(value = "org_code")
    private String tradingUnitCode;
    /**
     * 交易单元ID
     */
    private Long tradingUnitId;
    /**
     * 数据爬取时间
     */
    private LocalDateTime gatherTime;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;
}
