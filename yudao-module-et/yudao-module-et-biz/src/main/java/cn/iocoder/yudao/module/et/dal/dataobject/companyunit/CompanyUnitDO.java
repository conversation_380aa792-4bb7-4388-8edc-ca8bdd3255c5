package cn.iocoder.yudao.module.et.dal.dataobject.companyunit;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.sql.Timestamp;

/**
 * 区域公司超级表(省级披露数据) DO
 *
 * <AUTHOR>
 */
@TableName("company_unit")
@KeySequence("company_unit") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompanyUnitDO {

    /**
     * 时间
     */
    private Timestamp ts;
    /**
     * 公司id
     */
    private String companyId;
    /**
     * 新能源负荷预测
     */
    private Double loadForecast;

    /**
     * 系统负荷-B59联络线(省调负荷-日前)
     */
    private Float systemForecastB59;

    /**
     * 水电发电计划
     */
    private Float hydroelectricPowerGenerationPlan;

    /**
     * 新能源发电预测
     */
    private Float powerGenerationForecast;

    /**
     * 火电量小
     */
    private Float thermalPowerSmall;

    /**
     * 火电可调
     */
    private Float thermalPowerAdjustable;

    /**
     * 供需比
     */
    private Float supplyDemandRatio;

    /**
     * 竞价空间
     */
    private Float biddingSpace;

    /**
     * 超短期预测
     */
    private Float ultraShortTermForecast;

    /**
     * 日前统一结算点分区价格-河东
     */
    private Float hisUnifiedSettlementPointPartitionPricesHd;

    /**
     * 实时统一结算点分区价格-河东
     */
    private Float realUnifiedSettlementPointPartitionPricesHd;

    /**
     * 日前统一结算点分区价格-河西
     */
    private Float hisUnifiedSettlementPointPartitionPricesHx;

    /**
     * 实时统一结算点分区价格-河西
     */
    private Float realUnifiedSettlementPointPartitionPricesHx;

    /**
     * 日前统一结算点价格
     */
    private Float hisUnifiedSettlementPointPrice;

    /**
     * 实时统一结算点价格
     */
    private Float realUnifiedSettlementPointPrice;

    /**
     * 日前风电
     */
    private Float hisWindPower;

    /**
     * 日前光电
     */
    private Float hisPhotovoltaicPower;

    /**
     * 日前甘肃_新疆
     */
    private Float hisLlxGsXj;

    /**
     * 日前甘肃_青海
     */
    private Float hisLlxGsQh;

    /**
     * 日前甘肃_宁夏
     */
    private Float hisLlxGsNx;

    /**
     * 日前甘肃_陕西
     */
    private Float hisLlxGsSx;

    /**
     * 日前甘肃_湖南
     */
    private Float hisLlxGsHn;

    /**
     * 日前甘肃_山东
     **/
    @TableField(value = "his_llx_1")
    private Float hisLlxGsSd;

    /**
     * 实时甘肃_新疆
     */
    private Float realLlxGsXj;

    /**
     * 实时甘肃_青海
     */
    private Float realLlxGsQh;

    /**
     * 实时甘肃_宁夏
     */
    private Float realLlxGsNx;

    /**
     * 实时甘肃_陕西
     */
    private Float realLlxGsSx;

    /**
     * 实时甘肃_湖南
     */
    private Float realLlxGsHn;

    /**
     * 日内甘肃_山东
     **/
    @TableField(value = "real_llx_1")
    private Float realLlxGsSd;

    /**
     * 实时风电
     */
    private Float realWindPower;

    /**
     * 实时光电
     */
    private Float realPhotovoltaicPower;

    /**
     * 省火上旋
     */
    private Float provincialThermalPowerUpwardRotation;

    /**
     * 省水上旋
     */
    private Float hydroelectricPowerUpwardRotation;

    /**
     * 实时频率
     */
    private Float realFrequency;

    /**
     * 省调负荷(实时)
     */
    private Float realLoad;

    /**
     * 火电开机容量
     */
    private Float thermalOpenCapacity;

    /**
     * 水电开机容量
     */
    private Float hydroelectricOpenCapacity;

    /**
     * 实际总出力
     */
    private Float realTotalPower;

    /**
     * 非市场化机组实际出力
     */
    private Float actualOutputOfNonMarketOrientedUnits;

    /**
     * 水电实时总出力
     */
    private Float realHydroelectricTotalPower;

    /**
     * 日前水电总出力预测含抽蓄
     */
    private Float dailyHydropowerOutputForecast;

    /**
     * 周前水电总出力预测含抽蓄
     */
    private Float weeklyHydropowerOutputForecast;

    /**
     * 跨省区现货出清电力
     * 外送电(日前)
     */
    private Float crossProvincialSpotClearingOfElectricity;
    /**
     * 核电总出力（甘肃暂时没有，要预留计算）
     */
    private Float totalNuclearPowerOutput;

    /**
     * 日电力供需平衡预测
     */
    private Float supplyDemandImbalanceElectricPowerForecast;
    /**
     * 日电量供需平衡预测
     */
    private Float supplyDemandImbalanceElectricQuantityForecast;

    /**
     * 日系统负荷预测短期
     */
    private Float loadSystemForecastShortTerm;
    /**
     * 日系统负荷预测超短期
     */
    private Float loadSystemForecastUltraShortTerm;

    /**
     * 发电能力预测
     */
    private Float realTotalPowerPred;

    /**
     * 非市场机组总出力预测
     */
    private Float actualOutputOfNonMarketOrientedUnitsPred;

    /**
     * 对象内 负载率计算
     * <p>
     * 负载率 = 火电出力/火电开机
     * 火电出力 = 总出力（外综411）-非市场机组总出力（外综412）-新能源总出力（外综413）-水电总出力（外综414）-核电总出力（甘肃暂时没有，要预留计算）
     *
     * @return
     */
    public Float getAverageLoadRate() {
        float huoDianChuLi = this.realTotalPower - this.actualOutputOfNonMarketOrientedUnits - (this.realWindPower
            + this.realPhotovoltaicPower)
            - this.realHydroelectricTotalPower - this.totalNuclearPowerOutput;
        float fuZaiLv = 0;
        try {
            if (this.thermalOpenCapacity == 0) {
                return fuZaiLv;
            }
            fuZaiLv = huoDianChuLi / this.thermalOpenCapacity;
        } catch (Exception e) {
            System.out.printf("计算负载率异常:" + e.getLocalizedMessage());
        }
        if (fuZaiLv < 0) {
            fuZaiLv = 0;
        }
        return fuZaiLv;
    }

    /**
     * 对象内 日前负载率计算
     * 日前负载率=火电出力/火电开机
     * 火电出力(火电负荷)=总出力（外综3137）-非市场机组总出力（外综3138）-新能源总出力（外综313（10））-水电总出力（外综313（12））-核电总出力（甘肃暂时没有，要预留计算）
     *
     * @return
     */
    public Float getHisAverageLoadRate() {
        float hisHuoDianChuLi = this.realTotalPower - this.actualOutputOfNonMarketOrientedUnits - (this.hisWindPower
            + this.hisPhotovoltaicPower)
            - this.realHydroelectricTotalPower - 0;
        float fuZaiLv = 0;
        try {
            if (this.thermalOpenCapacity == 0) {
                return fuZaiLv;
            }
            fuZaiLv = hisHuoDianChuLi / this.thermalOpenCapacity;
        } catch (Exception e) {
            System.out.printf("计算日前负载率异常:" + e.getLocalizedMessage());
        }
        if (fuZaiLv < 0) {
            fuZaiLv = 0;
        }
        return fuZaiLv;
    }

    /**
     * 对象内 竞价空间实际计算
     * 竞价空间实际=省调负荷实际+(外送电实际-受电负荷实际=五个联络线的和)-(新能源负荷实际=实时风电+实时光电)；
     *
     * @return
     */
    public Float getRealBiddingSpace() {
        float realBiddingSpace = this.realLoad + this.realLlxGsHn + this.realLlxGsQh + this.realLlxGsNx + this.realLlxGsSx + this.realLlxGsXj - (this.realWindPower + this.realPhotovoltaicPower);
        if (realBiddingSpace < 0) {
            realBiddingSpace = 0;
        }
        return realBiddingSpace;
    }

    /**
     * 对象内 外送电实时计算
     * 五个联络线(正数外送，负数受电)
     *
     * @return
     */
    public Float getWaiSong() {
        float waiSong = 0f;
        if (this.realLlxGsHn > 0) {
            waiSong = waiSong + this.realLlxGsHn;
        }
        if (this.realLlxGsQh > 0) {
            waiSong = waiSong + this.realLlxGsQh;
        }
        if (this.realLlxGsNx > 0) {
            waiSong = waiSong + this.realLlxGsNx;
        }
        if (this.realLlxGsSx > 0) {
            waiSong = waiSong + this.realLlxGsSx;
        }
        if (this.realLlxGsXj > 0) {
            waiSong = waiSong + this.realLlxGsXj;
        }
        return waiSong;
    }
}
