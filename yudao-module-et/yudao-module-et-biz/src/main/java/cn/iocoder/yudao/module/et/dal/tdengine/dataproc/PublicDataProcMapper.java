package cn.iocoder.yudao.module.et.dal.tdengine.dataproc;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.dal.dataobject.clearingprice.ClearingPriceDO;
import cn.iocoder.yudao.module.et.dal.dataobject.clearingprice.ClearingPricePlusDO;
import cn.iocoder.yudao.module.et.dal.dataobject.clearingprice.SectionInfoDO;
import cn.iocoder.yudao.module.et.dal.dataobject.companyunit.CompanyUnit5DO;
import cn.iocoder.yudao.module.et.dal.dataobject.companyunit.CompanyUnitDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公有（省级披露）数据处理类
 *
 * <AUTHOR>
 * @date 2024-10-19
 **/
@DS("tdengine")
@Mapper
public interface PublicDataProcMapper extends BaseMapperX<CompanyUnitDO> {
    /**
     * GS-B-212 日前市场事前发布信息-新能源负荷预测
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO company_unit_${companyId}",
        "(ts, load_forecast)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.loadForecast})",
        "</foreach>",
        "</script>"
    })
    Integer insertNewEnergyLoadForecasting(@Param("companyId") String companyId, List<CompanyUnitDO> list);

    /**
     * GS-B-212 日前市场事前发布信息-新能源负荷预测（5分钟数据分辨率）
     * @param companyId
     * @param list
     * @return
     */
    @TenantIgnore
    @Insert({
            "<script>",
            "INSERT INTO company_unit_5_${companyId}",
            "(ts, load_forecast)",
            " VALUES ",
            "<foreach collection='list' item='item' separator=','> ",
            "(#{item.ts}, #{item.loadForecast})",
            "</foreach>",
            "</script>"
    })
    Integer insertNewEnergyLoadForecasting5(@Param("companyId") String companyId, List<CompanyUnit5DO> list);

    /**
     * GS-B-213 日前市场事前发布信息-供需分析电厂侧
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO company_unit_${companyId}",
        "(ts,system_forecast_b59,hydroelectric_power_generation_plan,power_generation_forecast,thermal_power_small,thermal_power_adjustable,supply_demand_ratio,bidding_space)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.systemForecastB59}, #{item.hydroelectricPowerGenerationPlan}, #{item.powerGenerationForecast}, #{item.thermalPowerSmall}, #{item.thermalPowerAdjustable}, #{item.supplyDemandRatio}, #{item.biddingSpace})",
        "</foreach>",
        "</script>"
    })
    Integer insertSupplyDemandAnalysis(@Param("companyId") String companyId, List<CompanyUnitDO> list);

    /**
     * GS-B-221 实时市场事前发布信息-超短期系统负荷预测
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO company_unit_${companyId}",
        "(ts,ultra_short_term_forecast)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.ultraShortTermForecast})",
        "</foreach>",
        "</script>"
    })
    Integer insertUstLoadForecasting(@Param("companyId") String companyId, List<CompanyUnitDO> list);

    /**
     * GS-B-271 统一结算点价格展示
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO company_unit_${companyId}",
        "(ts,his_unified_settlement_point_partition_prices_hd,real_unified_settlement_point_partition_prices_hd,his_unified_settlement_point_partition_prices_hx,real_unified_settlement_point_partition_prices_hx,his_unified_settlement_point_price,real_unified_settlement_point_price)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.hisUnifiedSettlementPointPartitionPricesHd}, #{item.realUnifiedSettlementPointPartitionPricesHd}, #{item.hisUnifiedSettlementPointPartitionPricesHx}, #{item.realUnifiedSettlementPointPartitionPricesHx}, #{item.hisUnifiedSettlementPointPrice}, #{item.realUnifiedSettlementPointPrice})",
        "</foreach>",
        "</script>"
    })
    Integer insertLocationalMarginalPrice(@Param("companyId") String companyId, List<CompanyUnitDO> list);

    /**
     * GS-A-04 电网运行-实际负荷及系统备用
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO company_unit_${companyId}",
        "(ts,provincial_thermal_power_upward_rotation,hydroelectric_power_upward_rotation,real_frequency,real_load)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.provincialThermalPowerUpwardRotation}, #{item.hydroelectricPowerUpwardRotation}, #{item.realFrequency}, #{item.realLoad})",
        "</foreach>",
        "</script>"
    })
    Integer insertLoadAndStandby(@Param("companyId") String companyId, List<CompanyUnitDO> list);

    /**
     * GS-A-07 电网运行-非市场机组总出力
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO company_unit_${companyId}",
        "(ts, actual_output_of_non_market_oriented_units)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.actualOutputOfNonMarketOrientedUnits})",
        "</foreach>",
        "</script>"
    })
    Integer insertNonMarketUnitTotalPower(@Param("companyId") String companyId, List<CompanyUnitDO> list);

    /**
     * GS-A-08 电网运行-水电总出力含抽蓄
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO company_unit_${companyId}",
        "(ts, real_hydroelectric_total_power)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.realHydroelectricTotalPower})",
        "</foreach>",
        "</script>"
    })
    Integer insertRealtimeHydropowerTotal(@Param("companyId") String companyId, List<CompanyUnitDO> list);

    /**
     * GS-A-16 供需与约束-预测信息-周前水电总出力预测含抽蓄
     **/
    @TenantIgnore
    @Insert({
            "<script>",
            "INSERT INTO company_unit_${companyId}",
            "(ts, weekly_hydropower_output_forecast)",
            " VALUES ",
            "<foreach collection='list' item='item' separator=','> ",
            "(#{item.ts}, #{item.weeklyHydropowerOutputForecast})",
            "</foreach>",
            "</script>"
    })
    Integer insertWeeklyHydropowerOutputForecast(@Param("companyId") String companyId, List<CompanyUnitDO> list);


    /**
     * GS-A-15 供需与约束-预测信息-日前水电总出力预测含抽蓄
     **/
    @TenantIgnore
    @Insert({
            "<script>",
            "INSERT INTO company_unit_${companyId}",
            "(ts, daily_hydropower_output_forecast)",
            " VALUES ",
            "<foreach collection='list' item='item' separator=','> ",
            "(#{item.ts}, #{item.dailyHydropowerOutputForecast})",
            "</foreach>",
            "</script>"
    })
    Integer insertDailyHydropowerOutputForecast(@Param("companyId") String companyId, List<CompanyUnitDO> list);


    /**
     * GS-A-17 供需与约束-预测信息-日系统负荷预测短期
     **/
    @TenantIgnore
    @Insert({
            "<script>",
            "INSERT INTO company_unit_${companyId}",
            "(ts, load_system_forecast_short_term)",
            " VALUES ",
            "<foreach collection='list' item='item' separator=','> ",
            "(#{item.ts}, #{item.loadSystemForecastShortTerm})",
            "</foreach>",
            "</script>"
    })
    Integer insertLoadSystemForecastShortTerm(@Param("companyId") String companyId, List<CompanyUnitDO> list);


    /**
     * GS-A-18 供需与约束-预测信息-日系统负荷预测超短期
     **/
    @TenantIgnore
    @Insert({
            "<script>",
            "INSERT INTO company_unit_${companyId}",
            "(ts, load_system_forecast_ultra_short_term)",
            " VALUES ",
            "<foreach collection='list' item='item' separator=','> ",
            "(#{item.ts}, #{item.loadSystemForecastUltraShortTerm})",
            "</foreach>",
            "</script>"
    })
    Integer insertLoadSystemForecastUltraShortTerm(@Param("companyId") String companyId, List<CompanyUnitDO> list);




    /**
     * GS-A-05 电网运行-新能源总出力
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO company_unit_${companyId}",
        "(ts,real_wind_power,real_photovoltaic_power)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.realWindPower}, #{item.realPhotovoltaicPower})",
        "</foreach>",
        "</script>"
    })
    Integer insertRealtimeNewEnergyTotal(@Param("companyId") String companyId, List<CompanyUnitDO> list);

    /**
     * GS-A-06 电网运行-机组状态及发电总出力
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO company_unit_${companyId}",
        "(ts,thermal_open_capacity,hydroelectric_open_capacity,real_total_power)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.thermalOpenCapacity}, #{item.hydroelectricOpenCapacity}, #{item.realTotalPower})",
        "</foreach>",
        "</script>"
    })
    Integer insertUnitStatusAndTotalPower(@Param("companyId") String companyId, List<CompanyUnitDO> list);

    /**
     * GS-A-02 供需与约束-预测信息-日前新能源出力预测
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO company_unit_${companyId}",
        "(ts,his_wind_power,his_photovoltaic_power)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.hisWindPower}, #{item.hisPhotovoltaicPower})",
        "</foreach>",
        "</script>"
    })
    Integer insertDayAheadNewEnergyTotal(@Param("companyId") String companyId, List<CompanyUnitDO> list);

    /**
     * GS-A-03 供需与约束-预测信息-省间联络线输电曲线预测
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO company_unit_${companyId}",
        "(ts,his_llx_gs_xj,his_llx_gs_qh,his_llx_gs_nx,his_llx_gs_sx,his_llx_gs_hn,his_llx_1,real_llx_gs_xj,real_llx_gs_qh,real_llx_gs_nx,real_llx_gs_sx,real_llx_gs_hn,real_llx_1)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.hisLlxGsXj}, #{item.hisLlxGsQh}, #{item.hisLlxGsNx}, #{item.hisLlxGsSx}, #{item.hisLlxGsHn}, #{item.hisLlxGsSd}, #{item.realLlxGsXj}, #{item.realLlxGsQh}, #{item.realLlxGsNx}, #{item.realLlxGsSx}, #{item.realLlxGsHn}, #{item.realLlxGsSd})",
        "</foreach>",
        "</script>"
    })
    Integer insertProvincialTieLine(@Param("companyId") String companyId, List<CompanyUnitDO> list);

    /**
     * GS-A-10 市场运营-交易组织及出清-现货市场申报及出清信息-日前各节点出清类信息
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO substation_${companyId}",
        "(ts,his_market_clearing_price_lt,his_market_clearing_price_hsg,his_market_clearing_price_jy,his_market_clearing_price_cx,his_market_clearing_price_xg,his_market_clearing_price_ly,his_market_clearing_price_cj,his_market_clearing_price_zc,his_market_clearing_price_yd,his_market_clearing_price_ws,his_market_clearing_price_bl,his_market_clearing_price_hl)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.hisMarketClearingPriceLt}, #{item.hisMarketClearingPriceHsg}, #{item.hisMarketClearingPriceJy}, #{item.hisMarketClearingPriceCx}, #{item.hisMarketClearingPriceXg}, #{item.hisMarketClearingPriceLy}, #{item.hisMarketClearingPriceCj}, #{item.hisMarketClearingPriceZc}, #{item.hisMarketClearingPriceYd}, #{item.hisMarketClearingPriceWs}, #{item.hisMarketClearingPriceBl}, #{item.hisMarketClearingPriceHl})",
        "</foreach>",
        "</script>"
    })
    Integer insertDayAheadNodeClearing(@Param("companyId") String companyId, List<ClearingPriceDO> list);


//    @TenantIgnore
//    @InterceptorIgnore(tenantLine = "true")
//    @Insert({
//            "<script>",
//            "INSERT INTO market_clearing_${companyId}_${tableName} USING market_clearing TAGS(#{companyId}, #{tableName}) ",
//            "(ts, day_ahead_nodal_electricity_price)",
//            "VALUES ",
//            "<foreach collection='list' item='item' separator=','>",
//            "(#{item.ts}, #{item.dayAheadNodalElectricityPrice})",
//            "</foreach>",
//            "</script>"
//    })
//    Integer insertDayAheadNodeClearingPlus(@Param("companyId") String companyId,
//                                           @Param("tableName") String tableName,
//                                           @Param("list") List<ClearingPricePlusDO> list);
    /**
     * GS-A-19 市场运营-交易组织及出清-现货市场申报及出清信息-日前各节点出清类信息
     **/
    @TenantIgnore
    @InterceptorIgnore(tenantLine = "true")
    @Insert({
            "<script>",
            "INSERT INTO market_clearing_${companyId}_${tableName} USING market_clearing TAGS(#{companyId}, #{tableName}) ",
            "(ts, day_ahead_nodal_electricity_price, day_ahead_electricity_price, day_ahead_congestion_price,",
            "day_ahead_network_loss_price, day_ahead_total_power_quantity, day_ahead_quantity_of_various_power_sources)",
            "VALUES ",
            "<foreach collection='list' item='item' separator=','>",
            "(#{item.ts}, #{item.dayAheadNodalElectricityPrice}, #{item.dayAheadElectricityPrice},",
            "#{item.dayAheadCongestionPrice}, #{item.dayAheadNetworkLossPrice},",
            " #{item.dayAheadTotalPowerQuantity},#{item.dayAheadQuantityOfVariousPowerSources})",
            "</foreach>",
            "</script>"
    })
    Integer insertDayAheadNodeClearingPlus(@Param("companyId") String companyId,
                                           @Param("tableName") String tableName,
                                           @Param("list") List<ClearingPricePlusDO> list);


    /**
     * GS-A-20 市场运营-交易组织及出清-现货市场申报及出清信息-日前各节点出清类信息
     **/
    @TenantIgnore
    @InterceptorIgnore(tenantLine = "true")
    @Insert({
            "<script>",
            "INSERT INTO market_clearing_${companyId}_${tableName} USING market_clearing TAGS(#{companyId}, #{tableName}) ",
            "(ts, real_nodal_electricity_price, real_electricity_price, real_congestion_price,",
            "real_network_loss_price, real_total_power_quantity, real_quantity_of_various_power_sources,real_frequency_regulation_mileage_price)",
            "VALUES ",
            "<foreach collection='list' item='item' separator=','>",
            "(#{item.ts}, #{item.realNodalElectricityPrice}, #{item.realElectricityPrice},",
            "#{item.realCongestionPrice}, #{item.realNetworkLossPrice},",
            " #{item.realTotalPowerQuantity},#{item.realQuantityOfVariousPowerSources},#{item.realFrequencyRegulationMileagePrice})",
            "</foreach>",
            "</script>"
    })
    Integer insertRealTimeNodeClearingProcPlus(@Param("companyId") String companyId,
                                           @Param("tableName") String tableName,
                                           @Param("list") List<ClearingPricePlusDO> list);


    /**
     * GS-A-19~20 市场运营-交易组织及出清-现货市场申报及出清信息-日前实时各节点出清断面信息
     **/
    @TenantIgnore
    @InterceptorIgnore(tenantLine = "true")
    @Insert({
            "<script>",
            "INSERT INTO section_info_${companyId}_${sectionCode} USING section_info TAGS(#{companyId}, #{sectionCode}) ",
            "(ts, day_ahead_congestion_state,real_congestion_state)",
            "VALUES ",
            "<foreach collection='list' item='item' separator=','>",
            "(#{item.ts}, #{item.dayAheadCongestionState}, #{item.realCongestionState})",
            "</foreach>",
            "</script>"
    })
    Integer insertSectionInfo(@Param("companyId") String companyId,
                                           @Param("sectionCode") String sectionCode,
                                           @Param("list") List<SectionInfoDO> list);


    /**
     * GS-A-11 市场运营-交易组织及出清-现货市场申报及出清信息-实时各节点出清类信息
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO substation_${companyId}",
        "(ts,market_clearing_price_lt,market_clearing_price_hsg,market_clearing_price_jy,market_clearing_price_cx,market_clearing_price_xg,market_clearing_price_ly,market_clearing_price_cj,market_clearing_price_zc,market_clearing_price_yd,market_clearing_price_ws,market_clearing_price_bl,market_clearing_price_hl)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.marketClearingPriceLt}, #{item.marketClearingPriceHsg}, #{item.marketClearingPriceJy}, #{item.marketClearingPriceCx}, #{item.marketClearingPriceXg}, #{item.marketClearingPriceLy}, #{item.marketClearingPriceCj}, #{item.marketClearingPriceZc}, #{item.marketClearingPriceYd}, #{item.marketClearingPriceWs}, #{item.marketClearingPriceBl}, #{item.marketClearingPriceHl})",
        "</foreach>",
        "</script>"
    })
    Integer insertRealTimeNodeClearing(@Param("companyId") String companyId, List<ClearingPriceDO> list);

    /**
     * GS-A-14 供需与约束-预测信息-日电力电量供需平衡预测
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO company_unit_${companyId}",
        "(ts,supply_demand_imbalance_electric_power_forecast,supply_demand_imbalance_electric_quantity_forecast)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.supplyDemandImbalanceElectricPowerForecast}, #{item.supplyDemandImbalanceElectricQuantityForecast})",
        "</foreach>",
        "</script>"
    })
    Integer insertSupplyDemandImbalanceForecast(@Param("companyId") String companyId, List<CompanyUnitDO> list);

    /**
     * GS-A-12 供需与约束 > 预测信息 > 发电能力预测
     */
    @TenantIgnore
    @Insert({
            "<script>",
            "INSERT INTO company_unit_${companyId}",
            "(ts,real_total_power_pred)",
            " VALUES ",
            "<foreach collection='list' item='item' separator=','> ",
            "(#{item.ts}, #{item.realTotalPowerPred})",
            "</foreach>",
            "</script>"
    })
    Integer insertPowerGenerationCapacityForecast(@Param("companyId") String companyId, List<CompanyUnitDO> list);

    /**
     * GS-A-13 供需与约束 > 预测信息 > 非市场机组总出力预测
     */
    @TenantIgnore
    @Insert({
            "<script>",
            "INSERT INTO company_unit_${companyId}",
            "(ts, actual_output_of_non_market_oriented_units_pred)",
            " VALUES ",
            "<foreach collection='list' item='item' separator=','> ",
            "(#{item.ts}, #{item.actualOutputOfNonMarketOrientedUnitsPred})",
            "</foreach>",
            "</script>"
    })
    Integer insertNonMarketUnitTotalPowerForecast(@Param("companyId") String companyId, List<CompanyUnitDO> list);
}
