package cn.iocoder.yudao.module.et.controller.admin.similardayprediction.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 相似日表 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SimilarDayPredictionRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "6764")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "预测日", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预测日")
    private LocalDateTime forecastDay;

    @Schema(description = "相似日", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("相似日")
    private LocalDateTime similarDay;

    @Schema(description = "相似度")
    @ExcelProperty("相似度")
    private Double similarity;

    @Schema(description = "相似度排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("相似度排序")
    private Integer sort;

    @Schema(description = "部门ID", example = "20464")
    @ExcelProperty("部门ID")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    @ExcelProperty("上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "数据添加时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("数据添加时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @ExcelProperty("修改时间")
    private LocalDateTime modificationTime;

}