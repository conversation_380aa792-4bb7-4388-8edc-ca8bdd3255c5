package cn.iocoder.yudao.module.et.controller.admin.powerstation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 场站信息新增/修改 Request VO")
@Data
public class PowerStationSaveReqVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "31824")
    private Integer id;

    @Schema(description = "场站编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "场站编码不能为空")
    private String code;

    @Schema(description = "场站简称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "场站简称不能为空")
    private String name;

    @Schema(description = "场站全称", example = "王五")
    private String fullName;

    @Schema(description = "场站调度名称", example = "芋艿")
    private String dispatchName;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "场站类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "场站类型不能为空")
    private String type;

    @Schema(description = "场站装机容量")
    private Double capacity;

    @Schema(description = "场站设备台数")
    private Integer quantity;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否禁用不能为空")
    private Boolean enabled;

    @Schema(description = "部门ID", example = "24667")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    private String orgCode;

}