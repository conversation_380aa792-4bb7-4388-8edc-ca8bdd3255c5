package cn.iocoder.yudao.module.et.service.dataproc.outer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingUnitOrg.TradingUnitOrgDO;
import cn.iocoder.yudao.module.et.dal.dataobject.transactionResults.DailyRollingResultDO;
import cn.iocoder.yudao.module.et.dal.dataobject.transactionResults.DailyRollingResultDetailDO;
import cn.iocoder.yudao.module.et.dal.mysql.tradingUnitOrg.TradingUnitOrgMapper;
import cn.iocoder.yudao.module.et.dal.mysql.transactionResults.DailyRollingResultDetailMapper;
import cn.iocoder.yudao.module.et.dal.mysql.transactionResults.DailyRollingResultMapper;
import cn.iocoder.yudao.module.et.service.dataproc.base.BaseDataProc;
import cn.iocoder.yudao.module.et.service.dataproc.outer.dto.DailyRollingResultDetailDto;
import cn.iocoder.yudao.module.et.service.dataproc.outer.dto.DailyRollingResultDto;
import cn.jy.soft.remote.bean.SocketPacket;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * GS-A-54 日滚动交易-日滚动交易结果查询
 *
 * <AUTHOR>
 * @date 2025-03-15
 **/
@Slf4j
@Service
@AllArgsConstructor
public class DailyRollingResultProc implements BaseDataProc {

    private final DailyRollingResultMapper dailyRollingResultMapper;

    private final DailyRollingResultDetailMapper dailyRollingResultDetailMapper;

    private final TradingUnitOrgMapper tradingUnitOrgMapper;

    @Override
    public boolean execute(SocketPacket dto) {
        long runtime = System.currentTimeMillis();
        try {
            TenantUtils.execute(dto.getTenantId(), () -> {
                JSONObject jsonObject = JSONUtil.parseObj(dto.getData());
                TradingUnitOrgDO tradingUnitOrgDO =
                    tradingUnitOrgMapper.selectByTradingUnitCode(dto.getOrgCode().toUpperCase(), dto.getTenantId());
                List<DailyRollingResultDto> resultDtoList;
                for (String bidDate : jsonObject.keySet()) {
                    for (String tradingSeq : jsonObject.getJSONObject(bidDate).keySet()) {
                        resultDtoList =
                            JSONUtil.toList(jsonObject.getJSONObject(bidDate).getStr(tradingSeq),
                                DailyRollingResultDto.class);
                        if (CollUtil.isNotEmpty(resultDtoList)) {
                            for (DailyRollingResultDto resultDto : resultDtoList) {
                                // 组装日滚动交易结果数据
                                DailyRollingResultDO resultDO = new DailyRollingResultDO()
                                    .setBidDate(LocalDate.parse(resultDto.getBidDate(), DatePattern.NORM_DATE_FORMATTER))
                                    .setTargetDate(LocalDate.parse(resultDto.getTargetDate(), DatePattern.NORM_DATE_FORMATTER))
                                    .setTradeSeq(resultDto.getTradeSeq())
                                    .setTimeSlot(resultDto.getTimeSlot())
                                    .setTradingDirection(resultDto.getTradingDirection())
                                    .setQuantity(new BigDecimal(resultDto.getQuantity()))
                                    .setPrice(new BigDecimal(resultDto.getPrice()))
                                    .setTenantId(dto.getTenantId())
                                    .setDeptId(tradingUnitOrgDO.getDeptId())
                                    .setTradingUnitCode(dto.getOrgCode())
                                    .setTradingUnitId(tradingUnitOrgDO.getId())
                                    .setGatherTime(DateUtil.toLocalDateTime(dto.getGatherTime()))
                                    .setModificationTime(LocalDateTime.now());
                                // 判断数据是否存在
                                List<DailyRollingResultDO> tempResultList =
                                    dailyRollingResultMapper.selectList(Wrappers.<DailyRollingResultDO>lambdaQuery()
                                        .eq(DailyRollingResultDO::getBidDate, resultDO.getBidDate())
                                        .eq(DailyRollingResultDO::getTargetDate, resultDO.getTargetDate())
                                        .eq(DailyRollingResultDO::getTradeSeq, resultDO.getTradeSeq())
                                        .eq(DailyRollingResultDO::getTimeSlot, resultDO.getTimeSlot())
                                        .eq(DailyRollingResultDO::getTradingDirection, resultDO.getTradingDirection())
                                        .eq(DailyRollingResultDO::getTenantId, resultDO.getTenantId())
                                        .eq(DailyRollingResultDO::getTradingUnitCode, resultDO.getTradingUnitCode())
                                        .eq(DailyRollingResultDO::getTradingUnitId, resultDO.getTradingUnitId()));
                                if (CollUtil.isNotEmpty(tempResultList)) {
                                    // 清除已存在数据
                                    dailyRollingResultMapper.deleteById(tempResultList.get(0).getId());
                                    dailyRollingResultDetailMapper.delete(
                                        Wrappers.<DailyRollingResultDetailDO>lambdaQuery()
                                            .eq(DailyRollingResultDetailDO::getPid, tempResultList.get(0).getId()));
                                }
                                // 插入日滚动交易结果数据和明细
                                dailyRollingResultMapper.insert(resultDO);
                                if (CollUtil.isNotEmpty(resultDto.getChildren())) {
                                    List<DailyRollingResultDetailDO> resultDetailDOList = CollUtil.newArrayList();
                                    for (DailyRollingResultDetailDto resultDetailDto : resultDto.getChildren()) {
                                        resultDetailDOList.add(new DailyRollingResultDetailDO()
                                            .setPid(resultDO.getId())
                                            .setTradingDirection(resultDetailDto.getTradingDirection())
                                            .setQuantity(new BigDecimal(resultDetailDto.getQuantity()))
                                            .setPrice(new BigDecimal(resultDetailDto.getPrice()))
                                            .setSubmissionTime(StrUtil.isNotBlank(resultDetailDto.getSubmissionTime()) ?
                                                LocalDateTime.parse(
                                                    StrUtil.subBefore(resultDetailDto.getSubmissionTime(), StrUtil.DOT,
                                                        false), DatePattern.NORM_DATETIME_FORMATTER) : null)
                                            .setTradeTime(StrUtil.isNotBlank(resultDetailDto.getTradeTime()) ?
                                                LocalDateTime.parse(
                                                    StrUtil.subBefore(resultDetailDto.getTradeTime(), StrUtil.DOT,
                                                        false), DatePattern.NORM_DATETIME_FORMATTER) : null)
                                            .setTenantId(dto.getTenantId())
                                            .setDeptId(tradingUnitOrgDO.getDeptId())
                                            .setTradingUnitCode(dto.getOrgCode())
                                            .setTradingUnitId(tradingUnitOrgDO.getId())
                                            .setGatherTime(DateUtil.toLocalDateTime(dto.getGatherTime()))
                                            .setModificationTime(LocalDateTime.now()));
                                    }
                                    dailyRollingResultDetailMapper.insertBatch(resultDetailDOList);
                                }
                            }
                        }
                    }
                }
                log.info("[{}] > 解析入库完成，用时 {} ms", dto.getBizCode(), System.currentTimeMillis() - runtime);
            });
        } catch (Exception ex) {
            log.error(StrUtil.format("[{}] > 解析出现异常", dto.getBizCode()), ex);
            return false;
        }
        return true;
    }
}
