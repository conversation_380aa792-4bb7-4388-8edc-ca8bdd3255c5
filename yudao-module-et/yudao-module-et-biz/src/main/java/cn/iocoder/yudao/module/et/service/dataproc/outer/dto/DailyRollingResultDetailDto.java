package cn.iocoder.yudao.module.et.service.dataproc.outer.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * DailyRollingResultDetailDto
 *
 * <AUTHOR>
 * @date 2025-04-07
 **/
@Accessors(chain = true)
@NoArgsConstructor
@Data
public class DailyRollingResultDetailDto {
    /**
     * 时段
     */
    private String timeSlot;
    /**
     * 买卖方向
     */
    private String tradingDirection;
    /**
     * 成交电量（MWh）
     */
    private String quantity;
    /**
     * 成交电价（元/MWh）
     */
    private String price;
    /**
     * 申报时间
     */
    private String submissionTime;
    /**
     * 成交时间
     */
    private String tradeTime;
}
