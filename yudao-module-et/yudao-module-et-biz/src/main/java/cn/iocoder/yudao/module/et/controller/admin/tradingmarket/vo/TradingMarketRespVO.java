package cn.iocoder.yudao.module.et.controller.admin.tradingmarket.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 交易 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TradingMarketRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "635")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "交易中心唯一编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易中心唯一编码")
    private String code;

    @Schema(description = "交易市场简称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("交易市场简称")
    private String name;

    @Schema(description = "交易市场全称", example = "李四")
    @ExcelProperty("交易市场全称")
    private String fullName;

    @Schema(description = "交易市场所在省份")
    @ExcelProperty("交易市场所在省份")
    private Integer districtCode;

    @Schema(description = "所在区域分组，东北、华北、华东……", example = "李四")
    @ExcelProperty("所在区域分组，东北、华北、华东……")
    private String groupName;

    @Schema(description = "算法，normal：正位预测算法；cross：错位预测算法")
    @ExcelProperty("算法，normal：正位预测算法；cross：错位预测算法")
    private String predictionModel;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}