package cn.iocoder.yudao.module.et.controller.admin.powerstation.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 场站信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PowerStationPageReqVO extends PageParam {

    @Schema(description = "场站编码")
    private String code;

    @Schema(description = "场站简称", example = "赵六")
    private String name;

    @Schema(description = "场站全称", example = "王五")
    private String fullName;

    @Schema(description = "场站调度名称", example = "芋艿")
    private String dispatchName;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "场站类型", example = "2")
    private String type;

    @Schema(description = "场站装机容量")
    private Double capacity;

    @Schema(description = "场站设备台数")
    private Integer quantity;

    @Schema(description = "是否禁用")
    private Boolean enabled;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "部门ID", example = "24667")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    private String orgCode;

}