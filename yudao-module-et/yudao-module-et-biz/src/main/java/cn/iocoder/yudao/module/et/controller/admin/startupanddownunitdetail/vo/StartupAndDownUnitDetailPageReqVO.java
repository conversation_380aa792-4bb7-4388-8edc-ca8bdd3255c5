package cn.iocoder.yudao.module.et.controller.admin.startupanddownunitdetail.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 必开必停机组明细信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StartupAndDownUnitDetailPageReqVO extends PageParam {

    @Schema(description = "父ID", example = "3992")
    private Long pid;

    @Schema(description = "必开 1 必停 0", example = "1")
    private String type;

    @Schema(description = "业务时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date[] bizDate;

    @Schema(description = "机组台数")
    private Integer units;

    @Schema(description = "电压等级(kV)")
    private BigDecimal voltageLevel;

    @Schema(description = "原因")
    private String cause;

    @Schema(description = "开始时间")
    private LocalDateTime dateBegin;

    @Schema(description = "结束时间")
    private LocalDateTime dateEnd;

    @Schema(description = "部门ID", example = "13853")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "数据爬取时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] gatherDate;

    @Schema(description = "修改时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] modificationTime;

}
