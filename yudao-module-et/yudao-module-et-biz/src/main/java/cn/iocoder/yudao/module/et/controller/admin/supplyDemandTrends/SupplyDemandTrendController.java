package cn.iocoder.yudao.module.et.controller.admin.supplyDemandTrends;

import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.et.service.supplyDemandTrends.SupplyDemandTrendsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotBlank;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Tag(name = "电力交易辅助 - 供需趋势页面")
@RestController
@RequestMapping("/et/supplyDemandTrend")
@Validated
public class SupplyDemandTrendController {

    @Resource
    SupplyDemandTrendsService supplyDemandTrendsService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 查询 直调用电分析
     *
     * @param timeStr 年 月 日
     * @return
     */
    @GetMapping("/directCallElectricalAnalysis")
    @Operation(summary = "供需趋势 直调用电分析")
    @PreAuthorize("@ss.hasPermission('et:supplyDemandTrend_directCallElectricalAnalysis:query')")
    public CommonResult<Object> getDirectCallElectricalAnalysis(@NotBlank String timeStr) {
        Object resultObject = new Object();

        String[] timeStrArr = timeStr.split("-");
        boolean isYear = timeStrArr.length != 2;

        resultObject = supplyDemandTrendsService.getDynamicField(timeStr, "systemForecastB59", "systemForecastB59", isYear);
        return CommonResult.success(resultObject);
    }

    /**
     * 查询 直调风电用电分析
     *
     * @param timeStr 年 月 日
     * @return
     */
    @GetMapping("/directWindPowerAnalysis")
    @Operation(summary = "供需趋势 直调风电用电分析")
    @PreAuthorize("@ss.hasPermission('et:supplyDemandTrend_directWindPowerAnalysis:query')")
    public CommonResult<Object> getDirectWindPowerAnalysis(@NotBlank String timeStr) {
        Object resultObject = new Object();

        String[] timeStrArr = timeStr.split("-");
        boolean isYear = timeStrArr.length != 2;
        resultObject = supplyDemandTrendsService.getDynamicField(timeStr, "hisWindPower", "hisWindPower", isYear);
        return CommonResult.success(resultObject);
    }

    /**
     * 查询 直调光伏用电分析
     *
     * @param timeStr 年 月 日
     * @return
     */
    @GetMapping("/directModulationPhotovoltaicAnalysis")
    @Operation(summary = "供需趋势 直调光伏用电分析")
    @PreAuthorize("@ss.hasPermission('et:supplyDemandTrend_directModulationPhotovoltaicAnalysis:query')")
    public CommonResult<Object> getDirectModulationPhotovoltaicAnalysis(@NotBlank String timeStr) {
        Object resultObject = new Object();

        String[] timeStrArr = timeStr.split("-");
        boolean isYear = timeStrArr.length != 2;
        resultObject = supplyDemandTrendsService.getDynamicField(timeStr, "hisPhotovoltaicPower", "hisPhotovoltaicPower", isYear);
        return CommonResult.success(resultObject);
    }

    /**
     * 查询 新能源
     *
     * @param timeStr 年 月 日
     * @return
     */
    @GetMapping("/newEnergyAnalysis")
    @Operation(summary = "供需趋势 新能源分析")
    @PreAuthorize("@ss.hasPermission('et:supplyDemandTrend_newEnergyAnalysis:query')")
    public CommonResult<Object> getNewEnergyAnalysis(@NotBlank String timeStr) {
        Object resultObject = new Object();

        String[] timeStrArr = timeStr.split("-");
        boolean isYear = timeStrArr.length != 2;
        resultObject = supplyDemandTrendsService.getDynamicField(timeStr, "loadForecast", "powerGenerationForecast", isYear);
        return CommonResult.success(resultObject);
    }

    /**
     * 查询 外送分析
     *
     * @param timeStr 年 月 日
     * @return
     */
    @GetMapping("/exportAnalysis")
    @Operation(summary = "供需趋势 外送分析")
    @PreAuthorize("@ss.hasPermission('et:supplyDemandTrend_exportAnalysis:query')")
    public CommonResult<Object> getExportAnalysis(@NotBlank String timeStr) {
        Object resultObject = new Object();

        String[] timeStrArr = timeStr.split("-");
        boolean isYear = timeStrArr.length != 2;
        resultObject = supplyDemandTrendsService.getDynamicField(timeStr, "crossProvincialSpotClearingOfElectricity", "crossProvincialSpotClearingOfElectricity", isYear);
        return CommonResult.success(resultObject);
    }

    /**
     * 查询 火电开机容量
     *
     * @param timeStr 年 月 日
     * @return
     */
    @GetMapping("/thermalPowerStartUpCapacity")
    @Operation(summary = "供需趋势 火电开机容量")
    @PreAuthorize("@ss.hasPermission('et:supplyDemandTrend_thermalPowerStartUpCapacity:query')")
    public CommonResult<Object> getThermalPowerStartUpCapacity(@NotBlank String timeStr) {
        Object resultObject = new Object();

        String[] timeStrArr = timeStr.split("-");
        boolean isYear = timeStrArr.length != 2;
        resultObject = supplyDemandTrendsService.getDynamicField(timeStr, "thermalOpenCapacity", "thermalOpenCapacity", isYear);
        return CommonResult.success(resultObject);

    }

    /**
     * 查询 火电开机台数
     *
     * @param timeStr 年 月 日
     * @return
     */
    @GetMapping("/numberOfThermalPowerUnits")
    @Operation(summary = "供需趋势 火电开机台数")
    @PreAuthorize("@ss.hasPermission('et:supplyDemandTrend_numberOfThermalPowerUnits:query')")
    public CommonResult<Object> getNumberOfThermalPowerUnits(@NotBlank String timeStr) {
        Object resultObject = new Object();

        String[] timeStrArr = timeStr.split("-");
        boolean isYear = timeStrArr.length != 2;
        resultObject = supplyDemandTrendsService.getDynamicField(timeStr, "crossProvincialSpotClearingOfElectricity", "crossProvincialSpotClearingOfElectricity", isYear);
        return CommonResult.success(resultObject);

    }


    /**
     * 查询 竞价空间
     *
     * @param timeStr 年 月 日
     * @return
     */
    @GetMapping("/biddingSpace")
    @Operation(summary = "供需趋势 竞价空间")
    @PreAuthorize("@ss.hasPermission('et:supplyDemandTrend_biddingSpace:query')")
    public CommonResult<Object> getBiddingSpace(@NotBlank String timeStr) {
        Object resultObject = new Object();

        String[] timeStrArr = timeStr.split("-");
        boolean isYear = timeStrArr.length != 2;
        resultObject = supplyDemandTrendsService.getDynamicField(timeStr, "biddingSpace", "biddingSpace", isYear);

        return CommonResult.success(resultObject);

    }

    /**
     * 查询 平均负载率
     *
     * @param timeStr 年 月 日
     * @return
     */
    @GetMapping("/averageLoadRate")
    @Operation(summary = "供需趋势 平均负载率")
    @PreAuthorize("@ss.hasPermission('et:supplyDemandTrend_averageLoadRate:query')")
    public CommonResult<Object> getAverageLoadRate(@NotBlank String timeStr) {
        Object resultObject = new Object();

        String[] timeStrArr = timeStr.split("-");
        boolean isYear = timeStrArr.length != 2;
        List<String> fieldList = new ArrayList<>();
        fieldList.add("realTotalPower");
        fieldList.add("actualOutputOfNonMarketOrientedUnits");
        fieldList.add("realWindPower");
        fieldList.add("realPhotovoltaicPower");
        fieldList.add("realHydroelectricTotalPower");
        fieldList.add("realHydroelectricTotalPower");
        resultObject = supplyDemandTrendsService.getDynamicField2(timeStr, fieldList, isYear);
        return CommonResult.success(resultObject);

    }

    /**
     * 查询 负荷+外送
     *
     * @param timeStr 年 月 日
     * @return
     */
    @GetMapping("/loadAndDelivery")
    @Operation(summary = "供需趋势 负荷+外送")
    @PreAuthorize("@ss.hasPermission('et:supplyDemandTrend_loadAndDelivery:query')")
    public CommonResult<Object> getLoadAndDelivery(@NotBlank String timeStr) {
        Object resultObject = new Object();

        String[] timeStrArr = timeStr.split("-");
        boolean isYear = timeStrArr.length != 2;
        resultObject = supplyDemandTrendsService.getDynamicField3(timeStr, "systemForecastB59", "crossProvincialSpotClearingOfElectricity", isYear);
        return CommonResult.success(resultObject);

    }

    /**
     * 查询 资源分析 - 天气数据 ws 总辐射
     *
     * @param areaTypeDTO
     * @return
     */
    @PostMapping("/etResources")
    @Operation(summary = "资源分析 - 天气数据 ws 总辐射")
    @PreAuthorize("@ss.hasPermission('et:supplyDemandTrend_etResources:query')")
    public CommonResult<Object> getEetResources(@RequestBody AreaTypeDTO areaTypeDTO) {
        Object resultObject = new Object();
        //获取当前用户缓存的查询树
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        stringRedisTemplate.opsForValue().set("et_resources_" + userId + "", JSONUtil.toJsonStr(areaTypeDTO.getAreaCode()));
        resultObject = supplyDemandTrendsService.getEetResources(areaTypeDTO);
        return CommonResult.success(resultObject);

    }


}
