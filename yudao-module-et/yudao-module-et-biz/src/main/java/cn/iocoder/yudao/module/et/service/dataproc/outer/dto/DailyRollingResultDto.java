package cn.iocoder.yudao.module.et.service.dataproc.outer.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * DailyRollingResult
 *
 * <AUTHOR>
 * @date 2025-04-07
 **/
@Accessors(chain = true)
@NoArgsConstructor
@Data
public class DailyRollingResultDto {
    /**
     * 申报日
     */
    private String bidDate;
    /**
     * 标的日
     */
    private String targetDate;
    /**
     * 交易名称
     */
    private String tradeSeq;
    /**
     * 时段
     */
    private String timeSlot;
    /**
     * 买卖方向
     */
    private String tradingDirection;
    /**
     * 成交电量（MWh）
     */
    private String quantity;
    /**
     * 成交均价（元/MWh）
     */
    private String price;
    /**
     * 日滚动交易明细
     */
    List<DailyRollingResultDetailDto> children;
}
