package cn.iocoder.yudao.module.et.dal.dataobject.transactionResults;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 日滚动交易结果 DO
 *
 * <AUTHOR>
 */
@TableName("jy_daily_rolling_result")
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class DailyRollingResultDO {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 申报日
     */
    private LocalDate bidDate;
    /**
     * 标的日
     */
    private LocalDate targetDate;
    /**
     * 交易名称
     */
    private String tradeSeq;
    /**
     * 时段
     */
    private String timeSlot;
    /**
     * 买卖方向
     */
    private String tradingDirection;
    /**
     * 成交电量（MWh）
     */
    private BigDecimal quantity;
    /**
     * 成交均价（元/MWh）
     */
    private BigDecimal price;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 交易单元编码
     */
    @TableField(value = "org_code")
    private String tradingUnitCode;
    /**
     * 交易单元ID
     */
    private Long tradingUnitId;
    /**
     * 数据爬取时间
     */
    private LocalDateTime gatherTime;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;
}
