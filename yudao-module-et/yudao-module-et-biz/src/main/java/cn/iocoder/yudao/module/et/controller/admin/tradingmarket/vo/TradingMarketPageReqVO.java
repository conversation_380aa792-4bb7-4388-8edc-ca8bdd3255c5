package cn.iocoder.yudao.module.et.controller.admin.tradingmarket.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 交易分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TradingMarketPageReqVO extends PageParam {

    @Schema(description = "交易中心唯一编码")
    private String code;

    @Schema(description = "交易市场简称", example = "赵六")
    private String name;

    @Schema(description = "交易市场全称", example = "李四")
    private String fullName;

    @Schema(description = "交易市场所在省份")
    private Integer districtCode;

    @Schema(description = "所在区域分组，东北、华北、华东……", example = "李四")
    private String groupName;

    @Schema(description = "算法，normal：正位预测算法；cross：错位预测算法")
    private String predictionModel;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}