package cn.iocoder.yudao.module.et.controller.admin.tradingmarket.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 交易新增/修改 Request VO")
@Data
public class TradingMarketSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "635")
    private Long id;

    @Schema(description = "交易中心唯一编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "交易中心唯一编码不能为空")
    private String code;

    @Schema(description = "交易市场简称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "交易市场简称不能为空")
    private String name;

    @Schema(description = "交易市场全称", example = "李四")
    private String fullName;

    @Schema(description = "交易市场所在省份")
    private Integer districtCode;

    @Schema(description = "所在区域分组，东北、华北、华东……", example = "李四")
    private String groupName;

    @Schema(description = "算法，normal：正位预测算法；cross：错位预测算法")
    private String predictionModel;

}