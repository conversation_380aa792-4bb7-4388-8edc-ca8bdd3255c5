import cn.hutool.core.convert.Convert;
import cn.iocoder.yudao.module.et.dal.dataobject.contractstype.ContractsTypeDO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractstype.ContractsTypeVO;
import cn.iocoder.yudao.module.et.dal.mysql.contractstype.ContractsTypeMapper;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.Test;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * GS-A-55 我的合同-当前合同
 *
 * <AUTHOR>
 * @date 2025-03-16
 **/
@Slf4j
@SpringBootTest(
        classes = {
                ContractsTypeMapper.class,
                DataSourceAutoConfiguration.class,
                MybatisPlusAutoConfiguration.class,
                DataSource.class,
                SqlSessionFactory.class
        }
)

@ActiveProfiles("local")
@MapperScan(basePackages = "cn.iocoder.yudao.module.et.dal.mysql")
public class ContractTypeTest {

    @Resource
    public ContractsTypeMapper contractsTypeMapper;

    @Test
    public void execute() {

        //System.out.println(queryMenuList(new ContractsTypeDO()));
        System.out.println("==================");

    }


    /**
     * 对象实体类转换
     *
     * @param rcMenu
     * @return
     */
    private ContractsTypeVO rcMenuTORcMenuVO(ContractsTypeDO rcMenu) {
        ContractsTypeVO rcMenuVO = new ContractsTypeVO();
        BeanUtils.copyProperties(rcMenu, rcMenuVO);
        return rcMenuVO;
    }


    /**
     * 根据一级递归调用子级
     * reversed方法 表示数字越大靠前
     *
     * @param menuList
     * @param rootMenuList
     * @return
     */
    private void findSubCategory(List<ContractsTypeDO> menuList, List<ContractsTypeVO> rootMenuList) {
        // 遍历一级
        for (ContractsTypeVO rcMenuVO : rootMenuList) {
            List<ContractsTypeVO> rcMenuVOList = new ArrayList<>();
            // 查找子级
            for (ContractsTypeDO rcMenu : menuList) {
                // 判断当前目录是否是子父级关系
                if (Objects.equals(rcMenu.getParentId(), Convert.toInt(rcMenuVO.getId()))) {
                    rcMenuVOList.add(rcMenuTORcMenuVO(rcMenu));
                }
                // 递归调用，不管有几级菜单，都能够适用
                findSubCategory(menuList, rcMenuVOList);
                // 类目显示排序，
                //rcMenuVOList.sort(Comparator.comparing(ContractsTypeVO::getSortOrder));
            }
            // 最后把查到的子级保存到一级目录中
            rcMenuVO.setChildren(rcMenuVOList);
        }
    }

    /**
     * 各级菜单列表展示
     *
     * @param menu
     * @param pageObject
     * @return
     */
    public List<ContractsTypeVO> queryMenuList(ContractsTypeDO menu) {
        // List<ContractsTypeDO> menuList = null;
        //Page<ContractsTypeDO> page = PageHelper.startPage(pageObject.getPageNum(), pageObject.getPageSize());
        // 将用户页面提交的参数拷贝到查询条件去
        // SpringBeanUtils.copyPropertiesIgnoreNull(pageObject, page);
        //先查出全部菜单
        // menuList = menuMapper.queryMenuList(menu);
        List<ContractsTypeDO> contractsTypeDOList = contractsTypeMapper.selectAll();
        //获取一级菜单	0代表一级菜单 .reversed()
        List<ContractsTypeVO> rootMenuList = contractsTypeDOList.stream()
                .filter(e -> e.getParentId().equals(0))
                .map(this::rcMenuTORcMenuVO)
                //.sorted(Comparator.comparing(ContractsTypeVO::getSortOrder))
                .collect(Collectors.toList());
        //查找字节点
        findSubCategory(contractsTypeDOList, rootMenuList);

        // 将真实分页信息拷贝到用户页面
        //SpringBeanUtils.copyPropertiesIgnoreNull(page, pageObject);
        //返回值
        return rootMenuList;
    }


    /**
     * 根据菜单id 查找子菜单
     *
     * @param menuId
     * @return
     */
    public List<ContractsTypeVO> queryMenuById(ContractsTypeDO menu) {
        //先查出菜单
        // List<ContractsTypeDO> menuList = menuMapper.queryMenuList(menu);
        List<ContractsTypeDO> contractsTypeDOList = contractsTypeMapper.selectAll();
        //获取一级菜单	0代表一级菜单 .reversed()
        List<ContractsTypeVO> rootMenuList = contractsTypeDOList.stream()
                .filter(e -> e.getParentId().equals(0))
                .map(this::rcMenuTORcMenuVO)
                //.sorted(Comparator.comparing(ContractsTypeVO::getSortOrder))
                .collect(Collectors.toList());
        //查找字节点
        findSubCategory(contractsTypeDOList, rootMenuList);
        //返回值
        List<ContractsTypeVO> rootMenu = new ArrayList<ContractsTypeVO>();
        for (int i = 0; i < rootMenuList.size(); i++) {
            if (rootMenuList.get(i).getId().equals(menu.getId())) {
                ContractsTypeVO rcMenuVO = rootMenuList.get(i);
                rootMenu.add(rcMenuVO);
            }
        }
        return rootMenu;
    }
}
