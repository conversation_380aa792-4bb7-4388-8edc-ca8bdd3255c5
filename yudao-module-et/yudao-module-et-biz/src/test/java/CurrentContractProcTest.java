import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.et.dal.dataobject.MarketMember.MarketMemberDO;
import cn.iocoder.yudao.module.et.dal.dataobject.contracts.ContractsDO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractsequipment.ContractsEquipmentDO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractsmember.ContractsMemberDO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractsmonth12.ContractsMonth12DO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractspowercurve.ContractsPowerCurveDO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractstime24.ContractsTime24DO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractstype.ContractsTypeDO;
import cn.iocoder.yudao.module.et.dal.dataobject.seriescontracts.SeriesContractsDO;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingUnitOrg.TradingUnitOrgDO;
import cn.iocoder.yudao.module.et.dal.mysql.MarketMember.MarketMemberMapper;
import cn.iocoder.yudao.module.et.dal.mysql.contracts.ContractsMapper;
import cn.iocoder.yudao.module.et.dal.mysql.contractsequipment.ContractsEquipmentMapper;
import cn.iocoder.yudao.module.et.dal.mysql.contractsmember.ContractsMemberMapper;
import cn.iocoder.yudao.module.et.dal.mysql.contractsmonth12.ContractsMonth12Mapper;
import cn.iocoder.yudao.module.et.dal.mysql.contractspowercurve.ContractsPowerCurveMapper;
import cn.iocoder.yudao.module.et.dal.mysql.contractstime24.ContractsTime24Mapper;
import cn.iocoder.yudao.module.et.dal.mysql.contractstype.ContractsTypeMapper;
import cn.iocoder.yudao.module.et.dal.mysql.seriescontracts.SeriesContractsMapper;
import cn.iocoder.yudao.module.et.dal.mysql.tradingUnitOrg.TradingUnitOrgMapper;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.Test;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * GS-A-55 我的合同-当前合同
 *
 * <AUTHOR>
 * @date 2025-03-16
 **/
@Slf4j
@SpringBootTest(
        classes = {
                ContractsMapper.class,
                ContractsMemberMapper.class,
                ContractsEquipmentMapper.class,
                ContractsMonth12Mapper.class,
                ContractsTime24Mapper.class,
                ContractsPowerCurveMapper.class,
                MarketMemberMapper.class,
                DataSourceAutoConfiguration.class,
                MybatisPlusAutoConfiguration.class,
                DataSource.class,
                SqlSessionFactory.class
        }
)
@ActiveProfiles("local")
@MapperScan(basePackages = "cn.iocoder.yudao.module.et.dal.mysql")
public class CurrentContractProcTest {
    @Resource
    public ContractsEquipmentMapper contractsEquipmentMapper;
    @Resource
    public ContractsMemberMapper contractsMemberMapper;
    @Resource
    public ContractsMonth12Mapper contractsMonth12Mapper;
    @Resource
    public ContractsTime24Mapper contractsTime24Mapper;
    @Resource
    public ContractsMapper contractsMapper;
    @Resource
    public ContractsPowerCurveMapper contractsPowerCurveMapper;
    @Resource
    public SeriesContractsMapper seriesContractsMapper;
    @Resource
    public ContractsTypeMapper contractsTypeMapper;

    @Resource
    public MarketMemberMapper marketMemberMapper;
    @Resource
    public TradingUnitOrgMapper tradingUnitOrgMapper;

    @Test
    public void execute() {
        String data = "{\"2025年1-12月祁韶直流配套新能源送湖南年度省间外送交易中节能敦煌#6站20MW光伏与国网湖南电力合同49\":{\"basic\":\"合同名称：,2025年1-12月祁韶直流配套新能源送湖南年度省间外送交易中节能敦煌#6站20MW光伏与国网湖南电力合同49,\\r\\n合同周期：,年度,\\r\\n合同类型：,省间外送交易合同,\\r\\n电量类型：,上网电量,\\r\\n合同序列：,2025年1-12月祁韶直流配套新能源送湖南年度省间外送交易合同序列,\\r\\n是否含税：,是,\\r\\n合同电量：,38210,\\r\\n合同电价：,285,\\r\\n目录电价：,,\\r\\n批复上网电价：,,\\r\\n价差：,,\\r\\n纸质合同编号：,PSGCC20241118A0002012010200049,\\r\\n合同编码：,PSGCC20241118A0002012010200049,\\r\\n交易编码：,PSGCC20241118A0002,\\r\\n开始日期：,2025-01-01,\\r\\n截至日期：,2025-12-31,\\r\\n签订状态：,已签订,\\r\\n备案状态：,已备案,\\r\\n签订时间：,2024-12-23 11:20:06,\\r\\n备案时间：,2024-12-23 11:20:06,\\r\\n\",\"party\":\"售电方：,中节能太阳能（敦煌）科技有限公司（二期）,\\r\\n购电方：,国网湖南省电力有限公司,\\r\\n售电单元：,中节能敦煌#6站20MW光伏,\\r\\n购电单元：,国网湖南电力,\\r\\n售方电价：,285,\\r\\n购方电价：,285,\\r\\n售方电量：,38210,\\r\\n购方电量：,38210,\\r\\n售方调整系数：,,\\r\\n购方调整系数：,,\\r\\n并网时间：,,\\r\\n\",\"monthly\":\"2025-01-01,2025-01-31,3368,285,\\r\\n2025-02-01,2025-02-28,2967,285,\\r\\n2025-03-01,2025-03-31,3415,285,\\r\\n2025-04-01,2025-04-30,2780,285,\\r\\n2025-05-01,2025-05-31,2811,285,\\r\\n2025-06-01,2025-06-30,3209,285,\\r\\n2025-07-01,2025-07-31,3550,285,\\r\\n2025-08-01,2025-08-31,3541,285,\\r\\n2025-09-01,2025-09-30,3248,285,\\r\\n2025-10-01,2025-10-31,3125,285,\\r\\n2025-11-01,2025-11-30,3036,285,\\r\\n2025-12-01,2025-12-31,3160,285,\\r\\n\",\"timeSlot\":\"1,段99,00:00-23:00,3368,3368,285,285,,2025-01-01,2025-01-31,\\r\\n1,段99,00:00-23:00,2967,2967,285,285,,2025-02-01,2025-02-28,\\r\\n1,段99,00:00-23:00,3415,3415,285,285,,2025-03-01,2025-03-31,\\r\\n1,段99,00:00-23:00,2780,2780,285,285,,2025-04-01,2025-04-30,\\r\\n1,段99,00:00-23:00,2811,2811,285,285,,2025-05-01,2025-05-31,\\r\\n\",\"generatorUnit\":\"售电方,中节能太阳能（敦煌）科技有限公司（二期）,中节能敦煌#6站20MW光伏,中节能敦煌二期20MW,太阳能,20,,2025-01-01,2025-01-31,3368,,,\\r\\n售电方,中节能太阳能（敦煌）科技有限公司（二期）,中节能敦煌#6站20MW光伏,中节能敦煌二期20MW,太阳能,20,,2025-02-01,2025-02-28,2967,,,\\r\\n售电方,中节能太阳能（敦煌）科技有限公司（二期）,中节能敦煌#6站20MW光伏,中节能敦煌二期20MW,太阳能,20,,2025-03-01,2025-03-31,3415,,,\\r\\n售电方,中节能太阳能（敦煌）科技有限公司（二期）,中节能敦煌#6站20MW光伏,中节能敦煌二期20MW,太阳能,20,,2025-04-01,2025-04-30,2780,,,\\r\\n售电方,中节能太阳能（敦煌）科技有限公司（二期）,中节能敦煌#6站20MW光伏,中节能敦煌二期20MW,太阳能,20,,2025-05-01,2025-05-31,2811,,,\\r\\n售电方,中节能太阳能（敦煌）科技有限公司（二期）,中节能敦煌#6站20MW光伏,中节能敦煌二期20MW,太阳能,20,,2025-06-01,2025-06-30,3209,,,\\r\\n售电方,中节能太阳能（敦煌）科技有限公司（二期）,中节能敦煌#6站20MW光伏,中节能敦煌二期20MW,太阳能,20,,2025-07-01,2025-07-31,3550,,,\\r\\n售电方,中节能太阳能（敦煌）科技有限公司（二期）,中节能敦煌#6站20MW光伏,中节能敦煌二期20MW,太阳能,20,,2025-08-01,2025-08-31,3541,,,\\r\\n售电方,中节能太阳能（敦煌）科技有限公司（二期）,中节能敦煌#6站20MW光伏,中节能敦煌二期20MW,太阳能,20,,2025-09-01,2025-09-30,3248,,,\\r\\n售电方,中节能太阳能（敦煌）科技有限公司（二期）,中节能敦煌#6站20MW光伏,中节能敦煌二期20MW,太阳能,20,,2025-10-01,2025-10-31,3125,,,\\r\\n售电方,中节能太阳能（敦煌）科技有限公司（二期）,中节能敦煌#6站20MW光伏,中节能敦煌二期20MW,太阳能,20,,2025-11-01,2025-11-30,3036,,,\\r\\n售电方,中节能太阳能（敦煌）科技有限公司（二期）,中节能敦煌#6站20MW光伏,中节能敦煌二期20MW,太阳能,20,,2025-12-01,2025-12-31,3160,,,\\r\\n\",\"accessoryBaseInfo\":\"{\\\"status\\\":0,\\\"message\\\":\\\"Success\\\",\\\"data\\\":{\\\"total\\\":12,\\\"list\\\":[{\\\"guid\\\":\\\"a724e1e9-ba0b-4c0f-b264-12f83426cbc7\\\",\\\"contractId\\\":\\\"d98f3f17-1312-4ee0-b44b-c203e3f0d6f1\\\",\\\"contractRole\\\":\\\"2\\\",\\\"membersId\\\":\\\"ff80808150ad641b01516bd4a3ac05a4\\\",\\\"unitId\\\":\\\"ff80808150ad641b01516bd6649605a6\\\",\\\"generatorName\\\":\\\"中节能敦煌二期20MW\\\",\\\"isRelation\\\":\\\"1\\\",\\\"operator\\\":\\\"0138A773EF89E4F3E050D40AC8D43BED\\\",\\\"operateDate\\\":\\\"2025-01-20 15:15:34\\\",\\\"generatorRatedCap\\\":20.00,\\\"generatorType\\\":\\\"050100\\\",\\\"currentApprlPrice\\\":null,\\\"startDate\\\":\\\"2025-11-01 00:00:00\\\",\\\"endDate\\\":\\\"2025-11-30 00:00:00\\\",\\\"unitPower\\\":3036.0000,\\\"tradeTimepart\\\":null,\\\"startTime\\\":null,\\\"endTime\\\":null,\\\"timeDivisionCode\\\":null,\\\"timeDivisionName\\\":null,\\\"timeDivisionRange\\\":null,\\\"transactionId\\\":null,\\\"vendeePrice\\\":null,\\\"salePrice\\\":null,\\\"tradePriceMargin\\\":null,\\\"contractPrice\\\":null,\\\"saleParticipantname\\\":\\\"中节能太阳能（敦煌）科技有限公司（二期）\\\",\\\"role\\\":\\\"售电方\\\",\\\"oneEnergy\\\":null,\\\"twoEnergy\\\":null,\\\"threeEnergy\\\":null,\\\"fourEnergy\\\":null,\\\"fiveEnergy\\\":null,\\\"sixEnergy\\\":null,\\\"sevenEnergy\\\":null,\\\"eightEnergy\\\":null,\\\"nineEnergy\\\":null,\\\"tenEnergy\\\":null,\\\"elevenEnergy\\\":null,\\\"twelveEnergy\\\":null,\\\"saleUnitName\\\":\\\"中节能敦煌#6站20MW光伏\\\",\\\"vendeeUnitName\\\":\\\"国网湖南电力\\\"},{\\\"guid\\\":\\\"dfc05544-041d-48d8-bb0f-1c7ebc66b066\\\",\\\"contractId\\\":\\\"d98f3f17-1312-4ee0-b44b-c203e3f0d6f1\\\",\\\"contractRole\\\":\\\"2\\\",\\\"membersId\\\":\\\"ff80808150ad641b01516bd4a3ac05a4\\\",\\\"unitId\\\":\\\"ff80808150ad641b01516bd6649605a6\\\",\\\"generatorName\\\":\\\"中节能敦煌二期20MW\\\",\\\"isRelation\\\":\\\"1\\\",\\\"operator\\\":\\\"0138A773EF89E4F3E050D40AC8D43BED\\\",\\\"operateDate\\\":\\\"2025-01-20 15:15:34\\\",\\\"generatorRatedCap\\\":20.00,\\\"generatorType\\\":\\\"050100\\\",\\\"currentApprlPrice\\\":null,\\\"startDate\\\":\\\"2025-12-01 00:00:00\\\",\\\"endDate\\\":\\\"2025-12-31 00:00:00\\\",\\\"unitPower\\\":3160.0000,\\\"tradeTimepart\\\":null,\\\"startTime\\\":null,\\\"endTime\\\":null,\\\"timeDivisionCode\\\":null,\\\"timeDivisionName\\\":null,\\\"timeDivisionRange\\\":null,\\\"transactionId\\\":null,\\\"vendeePrice\\\":null,\\\"salePrice\\\":null,\\\"tradePriceMargin\\\":null,\\\"contractPrice\\\":null,\\\"saleParticipantname\\\":\\\"中节能太阳能（敦煌）科技有限公司（二期）\\\",\\\"role\\\":\\\"售电方\\\",\\\"oneEnergy\\\":null,\\\"twoEnergy\\\":null,\\\"threeEnergy\\\":null,\\\"fourEnergy\\\":null,\\\"fiveEnergy\\\":null,\\\"sixEnergy\\\":null,\\\"sevenEnergy\\\":null,\\\"eightEnergy\\\":null,\\\"nineEnergy\\\":null,\\\"tenEnergy\\\":null,\\\"elevenEnergy\\\":null,\\\"twelveEnergy\\\":null,\\\"saleUnitName\\\":\\\"中节能敦煌#6站20MW光伏\\\",\\\"vendeeUnitName\\\":\\\"国网湖南电力\\\"}],\\\"pageNum\\\":2,\\\"pageSize\\\":10,\\\"size\\\":2,\\\"startRow\\\":11,\\\"endRow\\\":12,\\\"pages\\\":2,\\\"prePage\\\":1,\\\"nextPage\\\":0,\\\"isFirstPage\\\":false,\\\"isLastPage\\\":true,\\\"hasPreviousPage\\\":true,\\\"hasNextPage\\\":false,\\\"navigatePages\\\":8,\\\"navigatepageNums\\\":[1,2],\\\"navigateFirstPage\\\":1,\\\"navigateLastPage\\\":2}}\"}}";
        JSONObject jsonObject = JSONUtil.parseObj(data);
        ContractsDO contractsDO = new ContractsDO();    // 合同信息
        ContractsMemberDO contractsMemberDO = new ContractsMemberDO();    // 合同成员信息
        List<ContractsMonth12DO> contractsMonth12DOList = new ArrayList<>();    // 合同分月信息
        List<ContractsTime24DO> contractsTime24DOList = new ArrayList<>();   // 合同分时段信息
        List<ContractsEquipmentDO> contractsEquipmentDOList = new ArrayList<>();    // 合同机组信息
        List<ContractsPowerCurveDO> contractsPowerCurveDOList = new ArrayList<>();    // 合同机组信息
        SeriesContractsDO seriesContractsDONew = new SeriesContractsDO();    // 合同序列信息
        MarketMemberDO marketMemberDOSell = new MarketMemberDO();    // 售电方信息
        MarketMemberDO marketMemberDOPurchase = new MarketMemberDO();    // 购电方信息
        List<CsvRow> rowList;
        try {
            Long tenantId = 162L;
            String orgCode = "01";
            String bizCode = "GS-A-55";
            for (String bidDate : jsonObject.keySet()) {
                for (String tradingSeq : jsonObject.getJSONObject(bidDate).keySet()) {
                    rowList = CsvUtil.getReader(new CsvReadConfig().setBeginLineNo(0).setTrimField(true))
                            .readFromStr(jsonObject.getJSONObject(bidDate).getStr(tradingSeq)).getRows();
                    if (!rowList.isEmpty()) {
                        if ("basic".equals(tradingSeq)) {
                            contractsDO.setValueByRow(rowList);
                            contractsDO.setTenantId(tenantId);
                            contractsDO.setOrgCode(orgCode);
                            contractsDO.setDeleted(false);
                            if (bizCode.equals("GS-A-55")) {
                                contractsDO.setHistoryContract(false);
                            }
                            if (bizCode.equals("GS-A-56")) {
                                contractsDO.setHistoryContract(true);
                            }
                            ContractsTypeDO contractsTypeDO = contractsTypeMapper.selectByName(contractsDO.getContractsType());
                            if (contractsTypeDO == null) {
                                throw new RuntimeException("合同类型:" + contractsDO.getContractsType() + " 不存在");
                            }
                            contractsDO.setContractsTypeId(Convert.toInt(contractsTypeDO.getId()));
                            //查询合同序列
                            seriesContractsDONew = seriesContractsMapper.selectByName(contractsDO.getSeriesContracts());
                            if (seriesContractsDONew == null) {
                                seriesContractsDONew = new SeriesContractsDO();
                                seriesContractsDONew.setName(contractsDO.getSeriesContracts());
                                seriesContractsDONew.setDeleted(false);
                                seriesContractsDONew.setTenantId(tenantId);
                                seriesContractsDONew.setOrgCode(orgCode);
                                seriesContractsDONew.setContractsTypeId(contractsTypeDO.getId());
                                seriesContractsMapper.insert(seriesContractsDONew);
                            }
                            contractsDO.setSeriesContractsId(seriesContractsDONew.getId());

                            ContractsDO contractsDO1 = contractsMapper.selectByName(contractsDO.getName());
                            if (contractsDO1 != null) {
                                contractsDO.setId(contractsDO1.getId());
                                contractsMapper.updateById(contractsDO);
                            } else {
                                contractsMapper.insert(contractsDO);
                            }
                        }
                        // 合同方信息
                        if ("party".equals(tradingSeq)) {
                            contractsMemberDO.setValueByRow(rowList);
                            contractsMemberDO.setContractsId(contractsDO.getId());
                            contractsMemberDO.setTenantId(tenantId);
                            contractsMemberDO.setOrgCode(orgCode);
                            //查询售电交易单元
                            TradingUnitOrgDO sellerTradingUnitDO = tradingUnitOrgMapper.selectByTradingUnitOfficialName(contractsMemberDO.getSellerTradingUnitName());

                            marketMemberDOSell = marketMemberMapper.selectByName(contractsMemberDO.getSellerName(), contractsMemberDO.getSellerTradingUnitName());
                            if (marketMemberDOSell == null) {
                                marketMemberDOSell = new MarketMemberDO();
                                marketMemberDOSell.setName(contractsMemberDO.getSellerName());
                                marketMemberDOSell.setTradingUnitId(Convert.toStr(sellerTradingUnitDO.getId(), ""));
                                marketMemberDOSell.setCreateTime(LocalDateTime.now());
                                marketMemberDOSell.setUpdateTime(LocalDateTime.now());
                                marketMemberDOSell.setDeleted(false);
                                marketMemberMapper.insert(marketMemberDOSell);
                            }
                            TradingUnitOrgDO purchaserTradingUnitDO = tradingUnitOrgMapper.selectByTradingUnitOfficialName(contractsMemberDO.getPurchaserTradingUnitName());

                            marketMemberDOPurchase = marketMemberMapper.selectByName(contractsMemberDO.getPurchaserName(), contractsMemberDO.getPurchaserTradingUnitName());
                            if (marketMemberDOPurchase == null) {
                                marketMemberDOPurchase = new MarketMemberDO();
                                marketMemberDOPurchase.setName(contractsMemberDO.getPurchaserName());
                                if (purchaserTradingUnitDO != null) {
                                    marketMemberDOPurchase.setTradingUnitId(Convert.toStr(purchaserTradingUnitDO.getId(), ""));
                                }
                                marketMemberDOPurchase.setDeleted(false);
                                marketMemberDOPurchase.setCreateTime(LocalDateTime.now());
                                marketMemberDOPurchase.setUpdateTime(LocalDateTime.now());
                                marketMemberMapper.insert(marketMemberDOPurchase);
                            }

                            contractsMemberMapper.deleteByContractNo(contractsMemberDO.getContractsId());
                            contractsMemberMapper.insert(contractsMemberDO);
                        }
                        // 合同分月信息
                        if ("monthly".equals(tradingSeq)) {
                            rowList.forEach(row -> {
                                ContractsMonth12DO contractsMonth12DO = new ContractsMonth12DO();
                                contractsMonth12DO.setValueByRow(row);
                                contractsMonth12DO.setContractsId(contractsDO.getId());
                                contractsMonth12DO.setTenantId(tenantId);
                                contractsMonth12DO.setOrgCode(orgCode);
                                contractsMonth12DOList.add(contractsMonth12DO);
                            });
                            if (!contractsMonth12DOList.isEmpty()) {
                                contractsMonth12Mapper.deleteByContractNo(contractsDO.getId());
                                contractsMonth12Mapper.insertBatch(contractsMonth12DOList);
                            }
                        }
                        // 合同分时段信息
                        if ("timeSlot".equals(tradingSeq)) {
                            rowList.forEach(row -> {
                                ContractsTime24DO contractsTime24DO = new ContractsTime24DO();
                                contractsTime24DO.setValueByRow(row);
                                contractsTime24DO.setContractsId(contractsDO.getId());
                                contractsTime24DO.setTenantId(tenantId);
                                contractsTime24DO.setOrgCode(orgCode);
                                contractsTime24DOList.add(contractsTime24DO);
                            });
                            if (!contractsTime24DOList.isEmpty()) {
                                contractsTime24Mapper.deleteByContractNo(contractsDO.getId());
                                contractsTime24Mapper.insertBatch(contractsTime24DOList);
                            }
                        }
                        // 合同机组信息
                        if ("generatorUnit".equals(tradingSeq)) {
                            rowList.forEach(row -> {
                                ContractsEquipmentDO contractsEquipmentDO = new ContractsEquipmentDO();    // 时间信息
                                contractsEquipmentDO.setValueByRow(row);
                                contractsEquipmentDO.setContractsId(contractsDO.getId());
                                contractsEquipmentDO.setTenantId(tenantId);
                                contractsEquipmentDO.setOrgCode(orgCode);
                                contractsEquipmentDOList.add(contractsEquipmentDO);
                            });
                            if (!contractsEquipmentDOList.isEmpty()) {
                                contractsEquipmentMapper.deleteByContractNo(contractsDO.getId());
                                contractsEquipmentMapper.insertBatch(contractsEquipmentDOList);
                            }
                        }
                        // 合同机组信息
                        if ("accessoryBaseInfo".equals(tradingSeq)) {
                            JSONObject jsonObject1 = JSONUtil.parseObj(jsonObject.getJSONObject(bidDate).getStr(tradingSeq));
                            String message = Convert.toStr(jsonObject1.get("message"));
                            if (message.equals("Success")) {
                                JSONObject accessoryBaseInfoData = (JSONObject) jsonObject1.get("data");
                                JSONArray listArray = accessoryBaseInfoData.getJSONArray("list");
                                JSONObject recordObj = (JSONObject) listArray.get(0);
                                String membersId = recordObj.getStr("membersId");
                                String unitId = recordObj.getStr("unitId");
                                String generatorName = recordObj.getStr("generatorName");
                                String sysContractId = recordObj.getStr("contractId");
                                contractsDO.setSysContractId(sysContractId);
                                contractsMapper.updateById(contractsDO);
                                //市场主体名称
                                String saleParticipantname = recordObj.getStr("saleParticipantname");
                                //
                                String unitName = recordObj.getStr("saleUnitName");
                                MarketMemberDO marketMemberDO1 = marketMemberMapper.selectByName(saleParticipantname, unitName);
                                if (marketMemberDO1 != null) {
                                    marketMemberDO1.setSysMembersId(membersId);
                                    marketMemberDO1.setSysUnitId(unitId);
                                    marketMemberDO1.setSysUnitName(generatorName);
                                    marketMemberMapper.updateById(marketMemberDO1);
                                }
                            }
                        }
                    }
                }
            }
            // return true;
        } catch (Exception e) {
            log.error("合同数据解析异常，异常信息: {}", e);
        }

        // return false;
    }

}
